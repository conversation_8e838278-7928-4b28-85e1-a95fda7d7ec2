
// 统计页面逻辑
const { formatDateKey, getDateStart, getWeekStart, getMonthStart } = require('../../utils/helpers/time-utils.js')
const { CurrencyUtils } = require('../../utils/currency-utils.js')

Page({
  data: {
    // 加载状态
    isLoading: true,

    // 工作履历选择
    workHistories: [],
    selectedWorkIds: [],
    showWorkSelector: false,

    // 时间范围选择
    timeRangeOptions: [
      { key: 'all', text: '全部', active: false },
      { key: 'week', text: '本周', active: false },
      { key: 'month', text: '本月', active: true },
      { key: 'year', text: '本年', active: false },
      { key: 'custom', text: '自定义', active: false }
    ],
    currentTimeRange: 'month',

    // 热力图类型选择
    heatmapTypes: [
      { key: 'income', text: '收入', active: true, color: 'green' },
      { key: 'workTime', text: '工作时间', active: false, color: 'red' },
      { key: 'fishTime', text: '摸鱼时间', active: false, color: 'gold' },
      { key: 'hourlyRate', text: '平均时薪', active: false, color: 'green' }
    ],
    currentHeatmapType: 'income',
    
    // 统计数据
    statistics: {
      overview: {},
      timeType: {},
      incomeSource: {},
      fishing: {},
      workHistory: {},
      hourlyRate: {}
    },

    // 错误状态
    hasError: false,
    errorMessage: '',

    // 自定义日期选择器
    showDatePicker: false,
    customStartDate: '',
    customEndDate: '',
    customDateRangeText: '',

    // 货币设置
    currencySymbol: '¥'
  },

  /**
   * 页面加载时
   */
  onLoad: function(options) {
    // 初始化服务
    this.statisticsService = getApp().getStatisticsService()

    // 获取全局数据管理器
    this.dataManager = getApp().getDataManager()

    // 初始化页面
    this.initializePage()
  },

  /**
   * 页面显示时
   */
  onShow: function() {
    console.log('统计页面显示')

    // 页面显示时重新加载数据，确保数据是最新的
    if (this.dataManager && this.data.workHistories.length > 0) {
      this.loadStatistics()
    } else {
      // 如果数据还没准备好，重新初始化
      this.waitForDataManagerReady()
    }
  },

  /**
   * 页面卸载时
   */
  onUnload: function() {
    console.log('统计页面卸载')

    // 清理数据变化监听器
    if (this.dataChangeListener && this.dataManager && typeof this.dataManager.removeChangeListener === 'function') {
      this.dataManager.removeChangeListener(this.dataChangeListener)
      this.dataChangeListener = null
    }

    // 清理资源
    if (this.statisticsService) {
      this.statisticsService.clearCache()
    }
  },

  /**
   * 初始化页面
   */
  initializePage: function() {
    try {
      // 确保用户信息已加载
      this.ensureUserInfo()

      // 加载货币设置
      this.loadCurrencySettings()

      // 等待数据管理器就绪后再初始化
      this.waitForDataManagerReady()

    } catch (error) {
      console.error('页面初始化失败:', error)
      this.setData({
        hasError: true,
        errorMessage: '页面初始化失败',
        isLoading: false
      })
    }
  },

  /**
   * 加载货币设置
   */
  loadCurrencySettings: function() {
    try {
      CurrencyUtils.loadCurrencySettingsForContext(this, {
        includeSymbol: true
      })
    } catch (error) {
      console.error('加载货币设置失败:', error)
      // 使用默认值
      this.setData({
        currencySymbol: '¥'
      })
    }
  },

  /**
   * 等待数据管理器就绪
   */
  waitForDataManagerReady: function() {
    // 添加数据变化监听
    this.addDataChangeListener()

    // 初始化工作履历选择
    this.initializeWorkHistories()

    // 加载统计数据
    this.loadStatistics()
  },

  /**
   * 添加数据变化监听
   */
  addDataChangeListener: function() {
    if (this.dataManager && typeof this.dataManager.addChangeListener === 'function') {
      // 移除之前的监听器（如果存在）
      if (this.dataChangeListener) {
        this.dataManager.removeChangeListener(this.dataChangeListener)
      }

      // 添加新的监听器，但要避免无限循环
      this.dataChangeListener = () => {
        console.log('检测到数据变化')

        // 防止在初始化过程中触发无限循环
        if (this.isInitializing) {
          console.log('正在初始化中，跳过数据变化处理')
          return
        }

        // 延迟执行，避免立即触发
        setTimeout(() => {
          console.log('延迟重新初始化工作履历')
          this.initializeWorkHistories()
        }, 100)
      }

      this.dataManager.addChangeListener(this.dataChangeListener)
    }
  },

  /**
   * 初始化工作履历选择
   */
  initializeWorkHistories: function() {
    try {
      console.log('开始初始化工作履历选择')

      // 设置初始化状态，防止无限循环
      this.isInitializing = true

      // 确保数据管理器可用
      if (!this.dataManager) {
        this.dataManager = getApp().getDataManager()
      }

      // 获取工作履历数据
      const userData = this.dataManager.getUserData()
      if (!userData) {
        console.warn('用户数据未加载')
        this.isInitializing = false
        return
      }

      const workHistoryData = userData.workHistory || {}
      // 使用 DataManager 实时获取当前工作，避免缓存滞后
      let currentWorkId
      try {
        const currentWork = this.dataManager.getCurrentWork && this.dataManager.getCurrentWork()
        currentWorkId = currentWork ? currentWork.id : userData.currentWorkId
      } catch (e) {
        currentWorkId = userData.currentWorkId
      }

      console.log('工作履历数据:', {
        workHistoryCount: Object.keys(workHistoryData).length,
        currentWorkId: currentWorkId,
        workHistoryIds: Object.keys(workHistoryData)
      })

      // 如果没有工作履历数据
      if (Object.keys(workHistoryData).length === 0) {
        console.warn('没有工作履历数据')
        this.setData({
          workHistories: [],
          selectedWorkIds: [],
          hasError: true,
          errorMessage: '暂无工作履历数据，请先添加工作履历'
        })
        this.isInitializing = false
        return
      }

      // 如果没有设置当前工作ID，使用第一个工作履历作为默认选择（但不设置为当前工作）
      const workHistoryIds = Object.keys(workHistoryData)
      if (!currentWorkId && workHistoryIds.length > 0) {
        currentWorkId = workHistoryIds[0]
        console.log('使用第一个工作履历作为默认选择:', currentWorkId)
        // 注意：这里不调用setCurrentWork，避免修改数据
      }

      // 转换为数组格式
      const workHistories = Object.entries(workHistoryData).map(([id, history]) => {
        // 格式化日期显示
        const formatDate = (dateObj) => {
          if (!dateObj) return null
          if (typeof dateObj === 'string') return dateObj

          // 处理Date对象
          if (dateObj instanceof Date) {
            return formatDateKey(dateObj)
          }

          // 处理对象格式 {year, month, day}
          if (typeof dateObj === 'object' && dateObj.year && dateObj.month && dateObj.day) {
            return `${dateObj.year}-${String(dateObj.month).padStart(2, '0')}-${String(dateObj.day).padStart(2, '0')}`
          }

          return null
        }

        const startDateStr = formatDate(history.startDate)
        const endDateStr = formatDate(history.endDate)

        return {
          id: id,
          name: history.name || `${history.company} - ${history.position}`,
          company: history.company,
          position: history.position,
          startDate: startDateStr,
          endDate: endDateStr,
          isCurrent: id === currentWorkId,
          selected: (this.data.selectedWorkIds && this.data.selectedWorkIds.length > 0) ? this.data.selectedWorkIds.includes(id) : (id === currentWorkId),
          displayName: `${history.company} - ${history.position}`
        }
      })

      // 设置选中的工作履历ID
      const selectedWorkIds = workHistories
        .filter(history => history.selected)
        .map(history => history.id)

      console.log('工作履历初始化完成:', {
        workHistoriesCount: workHistories.length,
        selectedWorkIdsCount: selectedWorkIds.length,
        selectedWorkIds: selectedWorkIds
      })

      this.setData({
        workHistories: workHistories,
        selectedWorkIds: selectedWorkIds,
        hasError: false,
        errorMessage: ''
      })

      // 清除初始化状态
      this.isInitializing = false

    } catch (error) {
      console.error('初始化工作履历失败:', error)
      // 如果失败，设置空数组，不影响其他功能
      this.setData({
        workHistories: [],
        selectedWorkIds: [],
        hasError: true,
        errorMessage: '工作履历初始化失败'
      })

      // 清除初始化状态
      this.isInitializing = false
    }
  },

  /**
   * 确保用户信息已加载
   */
  async ensureUserInfo() {
    try {
      await getApp().ensureUserInfo()
    } catch (error) {
      console.error('确保用户信息失败:', error)
    }
  },

  /**
   * 加载统计数据
   */
  loadStatistics: function(showLoading = true) {
    console.log('=== 开始加载统计数据 ===')
    console.log('时间范围:', this.data.currentTimeRange, '选中工作履历:', this.data.selectedWorkIds)

    if (showLoading) {
      this.setData({
        isLoading: true,
        hasError: false
      })
    } else {
      this.setData({
        hasError: false
      })
    }

    try {
      // 计算日期范围
      const dateRange = this.calculateDateRange(this.data.currentTimeRange)

      // 确保数据管理器可用
      if (!this.dataManager) {
        this.dataManager = getApp().getDataManager()
      }

      // 检查工作履历是否已初始化
      if (!this.data.workHistories || this.data.workHistories.length === 0) {
        console.log('工作履历未初始化，先初始化工作履历')
        this.initializeWorkHistories()

        // 如果初始化后仍然没有工作履历，显示错误
        if (!this.data.workHistories || this.data.workHistories.length === 0) {
          this.setData({
            hasError: true,
            errorMessage: '暂无工作履历数据，请先添加工作履历',
            isLoading: false
          })
          return
        }
      }

      // 获取选中的工作履历ID
      let selectedWorkIds = this.data.selectedWorkIds || []
      if (selectedWorkIds.length === 0) {
        // 如果没有选中任何工作履历，使用当前工作履历或第一个工作履历
        const userData = this.dataManager.getUserData()
        const currentWorkId = userData?.currentWorkId
        if (currentWorkId) {
          selectedWorkIds = [currentWorkId]
        } else {
          // 使用第一个工作履历（但不设置为当前工作）
          const workHistoryKeys = Object.keys(userData?.workHistory || {})
          if (workHistoryKeys.length > 0) {
            selectedWorkIds = [workHistoryKeys[0]]
            console.log('使用第一个工作履历进行统计:', selectedWorkIds[0])
          }
        }
      }

      if (selectedWorkIds.length === 0) {
        throw new Error('没有可用的工作履历')
      }

      console.log('实际使用的工作履历ID:', selectedWorkIds)

      // 获取多个工作履历的统计数据并合并
      let mergedStatistics = null

      for (let i = 0; i < selectedWorkIds.length; i++) {
        const workId = selectedWorkIds[i]

        // 直接获取指定工作履历的统计数据
        const workStatistics = this.statisticsService.getComprehensiveStatistics(workId, dateRange)

        if (i === 0) {
          // 第一个工作履历，直接使用
          mergedStatistics = workStatistics
        } else {
          // 后续工作履历，合并数据
          mergedStatistics = this.mergeStatistics(mergedStatistics, workStatistics)
        }
      }

      console.log(`统计数据加载完成，选择了 ${selectedWorkIds.length} 个工作履历，总工作天数: ${mergedStatistics?.overview?.workDays || 0}`)

      // 格式化统计数据用于显示
      const formattedStatistics = this.formatStatisticsForDisplay(mergedStatistics)

      console.log('统计数据加载完成，工作天数:', formattedStatistics.overview.workDays)

      console.log('热力图数据检查:')
      console.log('calendarHeatmap存在:', !!formattedStatistics.calendarHeatmap)
      console.log('calendarData存在:', !!formattedStatistics.calendarHeatmap?.calendarData)
      console.log('calendarData长度:', formattedStatistics.calendarHeatmap?.calendarData?.length)
      console.log('weeks存在:', !!formattedStatistics.calendarHeatmap?.weeks)
      console.log('weeks长度:', formattedStatistics.calendarHeatmap?.weeks?.length)

      this.setData({
        statistics: formattedStatistics,
        isLoading: false
      }, () => {
        // 数据设置完成后，更新热力图
        this.updateHeatmapData()
      })

    } catch (error) {
      console.error('加载统计数据失败:', error)
      this.setData({
        hasError: true,
        errorMessage: error.message || '加载统计数据失败',
        isLoading: false
      })
    }
  },

  /**
   * 合并多个工作履历的统计数据
   */
  mergeStatistics: function(stats1, stats2) {
    try {
      // 深拷贝第一个统计数据
      const merged = JSON.parse(JSON.stringify(stats1))

      // 合并概览统计
      if (stats2.overview && merged.overview) {
        merged.overview.totalWorkHours = (merged.overview.totalWorkHours || 0) + (stats2.overview.totalWorkHours || 0)
        merged.overview.totalIncome = (merged.overview.totalIncome || 0) + (stats2.overview.totalIncome || 0)
        merged.overview.workDays = (merged.overview.workDays || 0) + (stats2.overview.workDays || 0)
        merged.overview.totalWorkMinutes = (merged.overview.totalWorkMinutes || 0) + (stats2.overview.totalWorkMinutes || 0)

        // 重新计算平均时薪
        if (merged.overview.totalWorkMinutes > 0) {
          merged.overview.averageHourlyRate = Math.round((merged.overview.totalIncome / (merged.overview.totalWorkMinutes / 60)) * 100) / 100
        }
      }

      // 合并收入来源分析
      if (stats2.incomeSourceAnalysis && merged.incomeSourceAnalysis) {
        merged.incomeSourceAnalysis.totalIncome = (merged.incomeSourceAnalysis.totalIncome || 0) + (stats2.incomeSourceAnalysis.totalIncome || 0)

        // 合并收入来源
        const mergedSources = [...(merged.incomeSourceAnalysis.sources || [])]
        const stats2Sources = stats2.incomeSourceAnalysis.sources || []

        stats2Sources.forEach(source2 => {
          const existingSource = mergedSources.find(s => s.name === source2.name)
          if (existingSource) {
            existingSource.totalIncome += source2.totalIncome
            existingSource.totalHours += source2.totalHours
            existingSource.sessions += source2.sessions
          } else {
            mergedSources.push({...source2})
          }
        })

        // 重新计算百分比和平均时薪
        mergedSources.forEach(source => {
          source.percentage = merged.incomeSourceAnalysis.totalIncome > 0 ?
            Math.round((source.totalIncome / merged.incomeSourceAnalysis.totalIncome) * 100) : 0
          source.avgHourlyRate = source.totalHours > 0 ?
            Math.round((source.totalIncome / source.totalHours) * 100) / 100 : 0
        })

        merged.incomeSourceAnalysis.sources = mergedSources.sort((a, b) => b.totalIncome - a.totalIncome)
      }

      // 合并摸鱼统计
      if (stats2.fishing && merged.fishing) {
        merged.fishing.totalFishingMinutes = (merged.fishing.totalFishingMinutes || 0) + (stats2.fishing.totalFishingMinutes || 0)
        merged.fishing.fishingCount = (merged.fishing.fishingCount || 0) + (stats2.fishing.fishingCount || 0)
        merged.fishing.fishingDays = (merged.fishing.fishingDays || 0) + (stats2.fishing.fishingDays || 0)

        // 重新计算平均值
        merged.fishing.averageFishingPerDay = merged.fishing.fishingDays > 0 ?
          Math.round((merged.fishing.fishingCount / merged.fishing.fishingDays) * 100) / 100 : 0
        merged.fishing.averageFishingDuration = merged.fishing.fishingCount > 0 ?
          Math.round((merged.fishing.totalFishingMinutes / merged.fishing.fishingCount) * 100) / 100 : 0
      }

      // 合并时薪统计（取平均值）
      if (stats2.hourlyRate && merged.hourlyRate) {
        const count1 = merged.hourlyRate.count || 0
        const count2 = stats2.hourlyRate.count || 0
        const totalCount = count1 + count2

        if (totalCount > 0) {
          merged.hourlyRate.count = totalCount
          merged.hourlyRate.average = Math.round(((merged.hourlyRate.average * count1 + stats2.hourlyRate.average * count2) / totalCount) * 100) / 100
          merged.hourlyRate.min = Math.min(merged.hourlyRate.min || Infinity, stats2.hourlyRate.min || Infinity)
          merged.hourlyRate.max = Math.max(merged.hourlyRate.max || 0, stats2.hourlyRate.max || 0)
        }
      }

      // 工作履历统计保持全局数据，不需要合并

      console.log('统计数据合并完成')
      return merged
    } catch (error) {
      console.error('合并统计数据失败:', error)
      return stats1 // 如果合并失败，返回第一个统计数据
    }
  },

  /**
   * 计算日期范围
   * @param {string} timeRange - 时间范围类型
   * @returns {Array|null} 日期范围 [startDate, endDate] 或 null
   */
  calculateDateRange: function(timeRange) {
    const now = new Date()

    switch (timeRange) {
      case 'week':
        const weekStart = getWeekStart(now)
        return [weekStart, getDateStart(now)]

      case 'month':
        const monthStart = getMonthStart(now)
        return [monthStart, getDateStart(now)]

      case 'year':
        const yearStart = new Date(now.getFullYear(), 0, 1)
        return [yearStart, getDateStart(now)]

      case 'custom':
        if (this.data.customStartDate && this.data.customEndDate) {
          const startDate = new Date(this.data.customStartDate)
          const endDate = new Date(this.data.customEndDate)
          // 确保结束日期包含当天的所有时间
          endDate.setHours(23, 59, 59, 999)
          return [startDate, endDate]
        }
        return null

      case 'all':
      default:
        return null // 返回null表示不限制日期范围
    }
  },

  /**
   * 显示/隐藏工作履历选择器
   */
  toggleWorkSelector: function() {
    this.setData({
      showWorkSelector: !this.data.showWorkSelector
    })
  },

  /**
   * 处理工作履历选择变化
   */
  onWorkHistoryToggle: function(event) {
    const workId = event.currentTarget.dataset.workId
    const selectedWorkIds = [...this.data.selectedWorkIds]

    if (selectedWorkIds.includes(workId)) {
      // 取消选择（但至少要保留一个）
      if (selectedWorkIds.length > 1) {
        const index = selectedWorkIds.indexOf(workId)
        selectedWorkIds.splice(index, 1)
      }
    } else {
      // 添加选择
      selectedWorkIds.push(workId)
    }

    // 更新工作履历的选中状态
    const updatedHistories = this.data.workHistories.map(history => ({
      ...history,
      selected: selectedWorkIds.includes(history.id)
    }))

    this.setData({
      workHistories: updatedHistories,
      selectedWorkIds: selectedWorkIds
    })

    // 重新加载统计数据，不显示加载状态避免页面跳转
    this.loadStatistics(false)
  },

  /**
   * 全选工作履历
   */
  selectAllWorkHistories: function() {
    const allWorkIds = this.data.workHistories.map(history => history.id)
    const updatedHistories = this.data.workHistories.map(history => ({
      ...history,
      selected: true
    }))

    this.setData({
      workHistories: updatedHistories,
      selectedWorkIds: allWorkIds
    })

    // 重新加载统计数据，不显示加载状态避免页面跳转
    this.loadStatistics(false)
  },

  /**
   * 清空选择（只保留当前工作）
   */
  clearWorkSelection: function() {},

  /**
   * 处理时间范围选择
   */
  onTimeRangeChange: function(event) {
    const { range } = event.currentTarget.dataset

    console.log('时间范围切换:', range)

    // 如果选择的是当前范围，不需要重新加载
    if (range === this.data.currentTimeRange) {
      return
    }

    // 如果选择自定义时间范围，显示日期选择器
    if (range === 'custom') {
      this.showCustomDatePicker()
      return
    }

    // 更新选中状态
    const timeRangeOptions = this.data.timeRangeOptions.map(option => ({
      ...option,
      active: option.key === range
    }))

    this.setData({
      timeRangeOptions: timeRangeOptions,
      currentTimeRange: range
    })

    // 重新加载统计数据，但不显示加载状态以避免页面跳转
    this.loadStatistics(false)
  },

  /**
   * 显示自定义日期选择器
   */
  showCustomDatePicker: function() {
    this.setData({
      showDatePicker: true
    })
  },

  /**
   * 日期范围确认
   */
  onDateRangeConfirm: function(event) {
    const { startDate, endDate } = event.detail

    // 更新时间范围选项，将自定义设为激活状态
    const timeRangeOptions = this.data.timeRangeOptions.map(option => ({
      ...option,
      active: option.key === 'custom'
    }))

    this.setData({
      showDatePicker: false,
      customStartDate: startDate,
      customEndDate: endDate,
      currentTimeRange: 'custom',
      timeRangeOptions: timeRangeOptions,
      customDateRangeText: this.formatCustomDateRangeText(startDate, endDate)
    })

    // 重新加载统计数据
    this.loadStatistics(false)
  },

  /**
   * 日期范围取消
   */
  onDateRangeCancel: function() {
    this.setData({
      showDatePicker: false
    })
  },

  /**
   * 生成自定义日期范围展示文本
   */
  formatCustomDateRangeText: function(startDate, endDate) {
    try {
      const s = new Date(startDate)
      const e = new Date(endDate)
      if (isNaN(s.getTime()) || isNaN(e.getTime())) return ''
      return `${s.getFullYear()}年${s.getMonth() + 1}月${s.getDate()}日 - ${e.getFullYear()}年${e.getMonth() + 1}月${e.getDate()}日`
    } catch (err) {
      return ''
    }
  },

  /**
   * 处理热力图类型切换
   */
  onHeatmapTypeChange: function(event) {
    const { type } = event.currentTarget.dataset

    console.log('热力图类型切换:', type)

    // 更新选中状态
    const heatmapTypes = this.data.heatmapTypes.map(option => ({
      ...option,
      active: option.key === type
    }))

    this.setData({
      heatmapTypes: heatmapTypes,
      currentHeatmapType: type
    })

    // 重新格式化热力图数据
    this.updateHeatmapData()
  },

  /**
   * 处理刷新操作
   */
  onRefresh: function() {
    console.log('手动刷新统计数据')
    
    // 清除缓存
    if (this.statisticsService) {
      this.statisticsService.clearCache()
    }
    
    // 重新加载数据
    this.loadStatistics()
  },

  /**
   * 处理统计卡片点击
   */
  onStatCardTap: function(event) {
    const { type } = event.currentTarget.dataset
    
    console.log('统计卡片点击:', type)
    
    // 这里可以添加跳转到详细页面的逻辑
    // 暂时只显示提示
    wx.showToast({
      title: '功能开发中',
      icon: 'none',
      duration: 1500
    })
  },

  /**
   * 格式化统计数据用于显示
   */
  formatStatisticsForDisplay: function(statistics) {
    const formatted = JSON.parse(JSON.stringify(statistics)) // 深拷贝

    // 格式化概览统计
    if (formatted.overview) {
      formatted.overview.totalWorkTimeText = this.formatDuration(formatted.overview.totalWorkMinutes)
      formatted.overview.totalIncomeText = this.formatNumber(formatted.overview.totalIncome)
      formatted.overview.averageHourlyRateText = this.formatNumber(formatted.overview.averageHourlyRate)
      formatted.overview.averageDailyIncomeText = this.formatNumber(formatted.overview.averageDailyIncome)
      formatted.overview.averageDailyHoursText = this.formatNumber(formatted.overview.averageDailyHours, 1) + '小时'
    }

    // 格式化时间类型统计
    if (formatted.timeType) {
      Object.keys(formatted.timeType).forEach(type => {
        if (formatted.timeType[type].minutes !== undefined) {
          formatted.timeType[type].durationText = this.formatDuration(formatted.timeType[type].minutes)
          formatted.timeType[type].percentageText = this.formatPercentage(formatted.timeType[type].percentage)
        }
      })
    }

    // 格式化收入来源统计
    if (formatted.incomeSource) {
      formatted.incomeSource.totalText = this.formatNumber(formatted.incomeSource.total)
      if (formatted.incomeSource.work) {
        formatted.incomeSource.work.amountText = this.formatNumber(formatted.incomeSource.work.amount)
        formatted.incomeSource.work.percentageText = this.formatPercentage(formatted.incomeSource.work.percentage)
      }
      if (formatted.incomeSource.overtime) {
        formatted.incomeSource.overtime.amountText = this.formatNumber(formatted.incomeSource.overtime.amount)
        formatted.incomeSource.overtime.percentageText = this.formatPercentage(formatted.incomeSource.overtime.percentage)
      }
    }

    // 格式化摸鱼统计
    if (formatted.fishing) {
      formatted.fishing.totalFishingTimeText = this.formatDuration(formatted.fishing.totalFishingMinutes)
      formatted.fishing.averageFishingDurationText = this.formatNumber(formatted.fishing.averageFishingDuration, 0) + '分钟'
      formatted.fishing.averageFishingPerDayText = this.formatNumber(formatted.fishing.averageFishingPerDay, 1)
    }

    // 格式化时薪统计
    if (formatted.hourlyRate) {
      formatted.hourlyRate.averageText = this.formatNumber(formatted.hourlyRate.average)
      formatted.hourlyRate.maxText = this.formatNumber(formatted.hourlyRate.max)
      formatted.hourlyRate.minText = this.formatNumber(formatted.hourlyRate.min)
    }

    // 格式化日历热力图数据
    if (formatted.calendarHeatmap && formatted.calendarHeatmap.calendarData) {
      formatted.calendarHeatmap.calendarData.forEach(dayData => {
        // 确保date是Date对象
        if (!(dayData.date instanceof Date)) {
          dayData.date = new Date(dayData.date)
        }

        dayData.workTimeText = this.formatDuration(dayData.workMinutes)
        dayData.incomeText = this.formatNumber(dayData.income)
        dayData.fishingTimeText = this.formatDuration(dayData.fishingMinutes)
        dayData.hourlyRateText = this.formatNumber(dayData.hourlyRate)

        // 默认显示收入类型
        dayData.intensity = dayData.intensities.income
        dayData.colorClass = this.getHeatmapColorClass(dayData.intensity, 'green')
        dayData.displayValue = dayData.income > 0 ? `${this.data.currencySymbol}${dayData.incomeText}` : ''
      })

      // 格式化日期范围文本
      try {
        const startDate = new Date(formatted.calendarHeatmap.dateRange.start)
        const endDate = new Date(formatted.calendarHeatmap.dateRange.end)

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          formatted.calendarHeatmap.dateRangeText = '日期范围'
        } else {
          formatted.calendarHeatmap.dateRangeText = `${startDate.getFullYear()}年${startDate.getMonth() + 1}月${startDate.getDate()}日 - ${endDate.getFullYear()}年${endDate.getMonth() + 1}月${endDate.getDate()}日`
        }
      } catch (error) {
        console.error('日期格式化失败:', error)
        formatted.calendarHeatmap.dateRangeText = '日期范围'
      }
    }

    // 格式化趋势分析数据
    if (formatted.trendAnalysis) {
      // 格式化趋势百分比
      Object.keys(formatted.trendAnalysis.trends || {}).forEach(key => {
        const trend = formatted.trendAnalysis.trends[key]
        formatted.trendAnalysis.trends[key + 'Text'] = trend > 0 ? `+${trend}%` : `${trend}%`
        formatted.trendAnalysis.trends[key + 'Direction'] = trend > 0 ? 'up' : trend < 0 ? 'down' : 'stable'
      })

      // 格式化平均值
      if (formatted.trendAnalysis.averages) {
        formatted.trendAnalysis.averages.workHoursText = `${formatted.trendAnalysis.averages.workHours}小时`
        formatted.trendAnalysis.averages.incomeText = this.formatNumber(formatted.trendAnalysis.averages.income)
        formatted.trendAnalysis.averages.efficiencyText = this.formatNumber(formatted.trendAnalysis.averages.efficiency)
      }
    }

    // 格式化收入增长趋势柱高，避免在模板中写复杂表达式
    if (formatted.incomeGrowthTrend && Array.isArray(formatted.incomeGrowthTrend.trendData)) {
       const avg = Number(formatted.incomeGrowthTrend.avgMonthlyIncome) || 0
       formatted.incomeGrowthTrend.trendData.forEach(item => {
         const denom = avg > 0 ? avg : (Number(item.income) > 0 ? Number(item.income) : 1)
         const h = Math.round((Number(item.income) / denom) * 50)
         item.barHeight = Math.max(4, h)
       })
    }

    // 格式化对比分析数据
    if (formatted.comparisonAnalysis) {
      Object.keys(formatted.comparisonAnalysis.changes || {}).forEach(key => {
        const change = formatted.comparisonAnalysis.changes[key]
        formatted.comparisonAnalysis.changes[key + 'Text'] = change > 0 ? `+${change}%` : `${change}%`
        formatted.comparisonAnalysis.changes[key + 'Direction'] = change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
      })
    }

    // 格式化排行榜数据
    if (formatted.rankingAnalysis) {
      ['topIncome', 'topWorkHours', 'topEfficiency', 'topFishing'].forEach(category => {
        if (formatted.rankingAnalysis[category]) {
          formatted.rankingAnalysis[category].forEach(record => {
            record.workHoursText = this.formatDuration(record.workMinutes)
            record.incomeText = this.formatNumber(record.income)
            record.efficiencyText = this.formatNumber(record.efficiency)
            record.fishingTimeText = this.formatDuration(record.fishingMinutes)
          })
        }
      })
    }

    // 格式化工作模式分析数据
    if (formatted.workPatternAnalysis) {
      // 格式化小时分布
      if (formatted.workPatternAnalysis.hourlyDistribution) {
        try {
          const hourlyMax = Math.max(
            ...formatted.workPatternAnalysis.hourlyDistribution.map(h => h.workMinutes || 0),
            0
          )
          formatted.workPatternAnalysis.hourlyMaxMinutes = hourlyMax

          formatted.workPatternAnalysis.hourlyDistribution.forEach(hour => {
            hour.timeLabel = this.formatHourLabel(hour.hour)
            hour.workTimeText = this.formatDuration(hour.workMinutes)
            hour.width = hourlyMax > 0 ? Math.round((hour.workMinutes / hourlyMax) * 100) : 0
            hour.styleWidth = `${hour.width}%`
          })
        } catch (e) {
          console.error('格式化小时分布失败:', e)
        }
      }

      // 格式化工作日分布
      if (formatted.workPatternAnalysis.weekdayDistribution) {
        try {
          const weekdayMax = Math.max(
            ...formatted.workPatternAnalysis.weekdayDistribution.map(d => d.workMinutes || 0),
            0
          )
          formatted.workPatternAnalysis.weekdayMaxMinutes = weekdayMax

          const weekdayLabels = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
          formatted.workPatternAnalysis.weekdayDistribution.forEach(day => {
            day.weekdayLabel = weekdayLabels[day.weekday] || '-'
            day.workTimeText = this.formatDuration(day.workMinutes)
            day.incomeText = this.formatNumber(day.income)
            day.avgWorkTimeText = `${day.avgWorkHours}小时`
            day.width = weekdayMax > 0 ? Math.round((day.workMinutes / weekdayMax) * 100) : 0
            day.styleWidth = `${day.width}%`
          })
        } catch (e) {
          console.error('格式化工作日分布失败:', e)
        }
      }
    }

    // 格式化效率分布数据
    if (formatted.efficiencyDistribution && formatted.efficiencyDistribution.ranges) {
      formatted.efficiencyDistribution.ranges.forEach(range => {
        range.totalHoursText = `${Math.round(range.totalHours * 100) / 100}小时`
        range.totalIncomeText = this.formatNumber(range.totalIncome)
        range.styleWidth = `${range.percentage}%`
      })
    }

    // 格式化收入来源分析数据（饼图）
    if (formatted.incomeSourceAnalysis && formatted.incomeSourceAnalysis.sources) {
      const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']

      formatted.incomeSourceAnalysis.sources.forEach((source, index) => {
        source.totalIncomeText = this.formatNumber(source.totalIncome)
        source.totalHoursText = `${Math.round(source.totalHours * 100) / 100}小时`
        source.avgHourlyRateText = this.formatNumber(source.avgHourlyRate)
        source.color = colors[index % colors.length]
      })
      formatted.incomeSourceAnalysis.totalIncomeText = this.formatNumber(formatted.incomeSourceAnalysis.totalIncome)

      // 计算饼图样式
      formatted.incomeSourceAnalysis.pieChartStyle = this.calculatePieChartStyles(
        formatted.incomeSourceAnalysis.sources,
        colors
      )
    }

    // 格式化时间分布饼图数据
    if (formatted.timeDistributionPie && formatted.timeDistributionPie.segments) {
      const timeColors = ['#3b82f6', '#ef4444', '#10b981']

      formatted.timeDistributionPie.segments.forEach(segment => {
        segment.hoursText = `${segment.hours}小时`
        segment.minutesText = this.formatDuration(segment.minutes)
      })
      formatted.timeDistributionPie.totalHoursText = `${formatted.timeDistributionPie.totalHours}小时`

      // 计算饼图样式
      formatted.timeDistributionPie.pieChartStyle = this.calculatePieChartStyles(
        formatted.timeDistributionPie.segments,
        timeColors
      )
    }

    // 格式化日期类型分布
    if (statistics.dateTypeDistribution && statistics.dateTypeDistribution.segments) {
      formatted.dateTypeDistribution = {
        ...statistics.dateTypeDistribution,
        segments: statistics.dateTypeDistribution.segments.map(segment => ({
          ...segment,
          percentageText: `${segment.percentage}%`
        }))
      }

      // 计算饼图样式
      const dateTypeColors = statistics.dateTypeDistribution.segments.map(segment => segment.color)
      formatted.dateTypeDistribution.pieChartStyle = this.calculatePieChartStyles(
        formatted.dateTypeDistribution.segments,
        dateTypeColors
      )
    }

    // 格式化收入调整统计
    if (statistics.incomeAdjustments) {
      formatted.incomeAdjustments = {
        ...statistics.incomeAdjustments
      }

      // 格式化收入类型饼图
      if (statistics.incomeAdjustments.extraIncomeSegments && statistics.incomeAdjustments.extraIncomeSegments.length > 0) {
        const extraIncomeColors = statistics.incomeAdjustments.extraIncomeSegments.map(segment => segment.color)
        formatted.incomeAdjustments.extraIncomePieStyle = this.calculatePieChartStyles(
          statistics.incomeAdjustments.extraIncomeSegments,
          extraIncomeColors
        )
      }

      // 格式化扣款类型饼图
      if (statistics.incomeAdjustments.deductionSegments && statistics.incomeAdjustments.deductionSegments.length > 0) {
        const deductionColors = statistics.incomeAdjustments.deductionSegments.map(segment => segment.color)
        formatted.incomeAdjustments.deductionPieStyle = this.calculatePieChartStyles(
          statistics.incomeAdjustments.deductionSegments,
          deductionColors
        )
      }
    }

    // 格式化历史总览数据
    if (formatted.historical) {
      // 全时数据
      if (formatted.historical.allTime) {
        formatted.historical.allTime.totalWorkTimeText = this.formatDuration(formatted.historical.allTime.totalWorkMinutes)
        formatted.historical.allTime.totalIncomeText = this.formatNumber(formatted.historical.allTime.totalIncome)
        formatted.historical.allTime.averageHourlyRateText = this.formatNumber(formatted.historical.allTime.averageHourlyRate)
      }

      // 最佳记录
      if (formatted.historical.bestRecords) {
        const records = formatted.historical.bestRecords
        if (records.highestIncome) {
          records.highestIncome.amountText = this.formatNumber(records.highestIncome.amount)
        }
        if (records.longestWork) {
          records.longestWork.timeText = this.formatDuration(records.longestWork.minutes)
        }
        if (records.bestEfficiency) {
          records.bestEfficiency.rateText = this.formatNumber(records.bestEfficiency.rate)
        }
      }

      // 里程碑数据
      if (formatted.historical.milestones && formatted.historical.milestones.firstWorkDate) {
        const firstDate = new Date(formatted.historical.milestones.firstWorkDate)
        formatted.historical.milestones.firstWorkDateText = `${firstDate.getFullYear()}年${firstDate.getMonth() + 1}月${firstDate.getDate()}日`

        // 计算工作天数
        const daysSinceStart = Math.floor((new Date() - firstDate) / (1000 * 60 * 60 * 24))
        formatted.historical.milestones.daysSinceStart = daysSinceStart
      }
    }

    // 添加收入分解数据
    this.addIncomeBreakdownData(formatted)

    return formatted
  },

  /**
   * 添加收入分解数据
   */
  addIncomeBreakdownData: function(statistics) {
    try {
      // 现在统计服务已经包含了收入调整，直接从 incomeSource 中获取数据
      const incomeSource = statistics.incomeSource || {}

      const baseIncome = (incomeSource.work?.amount || 0) + (incomeSource.overtime?.amount || 0)
      const extraIncome = incomeSource.extraIncome?.amount || 0
      const deductions = incomeSource.deductions?.amount || 0
      const adjustmentBalance = extraIncome - deductions
      const hasAdjustments = extraIncome > 0 || deductions > 0

      // 获取收入调整的详细类型统计
      const { IncomeAdjustmentService } = require('../../core/services/income-adjustment-service.js')
      const incomeAdjustmentService = new IncomeAdjustmentService()
      const dateRange = this.calculateDateRange(this.data.currentTimeRange)
      const selectedWorkIds = this.data.selectedWorkIds || []

      let allExtraIncomeByType = {}
      let allDeductionsByType = {}

      // 遍历所有选中的工作履历获取类型统计
      selectedWorkIds.forEach(workId => {
        const adjustmentStats = incomeAdjustmentService.calculateAdjustmentStatistics(dateRange, workId)

        // 合并按类型统计
        Object.entries(adjustmentStats.extraIncomeByType || {}).forEach(([type, data]) => {
          if (!allExtraIncomeByType[type]) {
            allExtraIncomeByType[type] = {
              amount: 0,
              count: 0,
              typeName: data.typeName
            }
          }
          allExtraIncomeByType[type].amount += data.amount
          allExtraIncomeByType[type].count += data.count
        })

        Object.entries(adjustmentStats.deductionsByType || {}).forEach(([type, data]) => {
          if (!allDeductionsByType[type]) {
            allDeductionsByType[type] = {
              amount: 0,
              count: 0,
              typeName: data.typeName
            }
          }
          allDeductionsByType[type].amount += data.amount
          allDeductionsByType[type].count += data.count
        })
      })

      // 创建收入分解数据
      statistics.incomeBreakdown = {
        baseIncome: baseIncome,
        baseIncomeText: this.formatNumber(baseIncome),
        extraIncome: extraIncome,
        extraIncomeText: this.formatNumber(extraIncome),
        deductions: deductions,
        deductionsText: this.formatNumber(deductions),
        adjustmentBalance: adjustmentBalance,
        adjustmentBalanceText: this.formatNumber(Math.abs(adjustmentBalance)),
        hasAdjustments: hasAdjustments,
        extraIncomeByType: allExtraIncomeByType,
        deductionsByType: allDeductionsByType
      }

      // 转换为数组格式用于显示
      statistics.incomeBreakdown.extraIncomeTypesList = Object.entries(allExtraIncomeByType).map(([type, data]) => ({
        type: type,
        typeName: data.typeName,
        amount: data.amount,
        amountText: this.formatNumber(data.amount),
        count: data.count
      }))

      statistics.incomeBreakdown.deductionsTypesList = Object.entries(allDeductionsByType).map(([type, data]) => ({
        type: type,
        typeName: data.typeName,
        amount: data.amount,
        amountText: this.formatNumber(data.amount),
        count: data.count
      }))

    } catch (error) {
      console.error('添加收入分解数据失败:', error)
      // 如果失败，设置空的收入分解数据
      statistics.incomeBreakdown = {
        baseIncome: statistics.overview?.totalIncome || 0,
        baseIncomeText: this.formatNumber(statistics.overview?.totalIncome || 0),
        extraIncome: 0,
        extraIncomeText: '0.00',
        deductions: 0,
        deductionsText: '0.00',
        adjustmentBalance: 0,
        adjustmentBalanceText: '0.00',
        hasAdjustments: false,
        extraIncomeByType: {},
        deductionsByType: {},
        extraIncomeTypesList: [],
        deductionsTypesList: []
      }
    }
  },

  /**
   * 格式化数字显示
   */
  formatNumber: function(num, decimals = 2) {
    if (typeof num !== 'number' || isNaN(num)) {
      return '0'
    }

    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万'
    }

    return num.toFixed(decimals)
  },

  /**
   * 格式化时长显示
   */
  formatDuration: function(minutes) {
    if (typeof minutes !== 'number' || minutes <= 0) {
      return '0小时'
    }
    
    const hours = Math.floor(minutes / 60)
    const mins = Math.round(minutes % 60)
    
    if (hours > 0 && mins > 0) {
      return `${hours}小时${mins}分钟`
    } else if (hours > 0) {
      return `${hours}小时`
    } else {
      return `${mins}分钟`
    }
  },

  /**
   * 格式化百分比显示
   */
  formatPercentage: function(percentage) {
    if (typeof percentage !== 'number' || isNaN(percentage)) {
      return '0%'
    }
    
    return percentage.toFixed(1) + '%'
  },

  /**
   * 格式化小时标签
   */
  formatHourLabel: function(hour) {
    if (typeof hour !== 'number' || hour < 0 || hour > 23) {
      return '00:00'
    }

    return hour.toString().padStart(2, '0') + ':00'
  },

  /**
   * 获取热力图颜色强度类名
   */
  getHeatmapColorClass: function(intensity, colorType = 'blue') {
    const baseClass = `heat-${colorType}`
    if (intensity >= 80) return `${baseClass}-very-high`
    if (intensity >= 60) return `${baseClass}-high`
    if (intensity >= 40) return `${baseClass}-medium`
    if (intensity >= 20) return `${baseClass}-low`
    if (intensity > 0) return `${baseClass}-very-low`
    return 'heat-none'
  },

  /**
   * 计算强度百分比
   */
  calculateIntensity: function(value, maxValue) {
    if (maxValue <= 0) return 0
    return Math.min(Math.round((value / maxValue) * 100), 100)
  },

  /**
   * 更新热力图数据
   */
  updateHeatmapData: function() {
    const statistics = this.data.statistics
    const currentType = this.data.currentHeatmapType

    if (!statistics.calendarHeatmap || !statistics.calendarHeatmap.calendarData) {
      return
    }

    // 获取当前类型的颜色
    const currentTypeConfig = this.data.heatmapTypes.find(t => t.key === currentType)
    const colorType = currentTypeConfig ? currentTypeConfig.color : 'green'

    // 更新日历热力图数据
    const updatedCalendarData = statistics.calendarHeatmap.calendarData.map(dayData => {
      let intensity = 0
      let displayValue = ''

      switch (currentType) {
        case 'income':
          intensity = dayData.intensities.income
          displayValue = dayData.income > 0 ? `${this.data.currencySymbol}${dayData.incomeText}` : ''
          break
        case 'workTime':
          intensity = dayData.intensities.workTime
          displayValue = dayData.workMinutes > 0 ? `${dayData.workHours}h` : ''
          break
        case 'fishTime':
          intensity = dayData.intensities.fishTime
          displayValue = dayData.fishingMinutes > 0 ? `${dayData.fishingTimeText}` : ''
          break
        case 'hourlyRate':
          intensity = dayData.intensities.hourlyRate
          displayValue = dayData.hourlyRate > 0 ? `${this.data.currencySymbol}${dayData.hourlyRateText}` : ''
          break
      }

      return {
        ...dayData,
        intensity: intensity,
        displayValue: displayValue,
        colorClass: this.getHeatmapColorClass(intensity, colorType)
      }
    })

    // 重新组织为周数据
    const updatedWeeks = []
    for (let i = 0; i < updatedCalendarData.length; i += 7) {
      updatedWeeks.push(updatedCalendarData.slice(i, i + 7))
    }

    // 更新数据
    this.setData({
      'statistics.calendarHeatmap.calendarData': updatedCalendarData,
      'statistics.calendarHeatmap.weeks': updatedWeeks
    })
  },

  /**
   * 计算饼图样式（移除DOM查询，改用数据绑定）
   */
  calculatePieChartStyles: function(segments, colors) {
    try {
      if (!segments || segments.length === 0) {
        return 'background: #f0f0f0;'
      }

      let currentAngle = 0
      let gradientStops = []

      segments.forEach((segment, index) => {
        const percentage = segment.percentage || 0
        const angle = (percentage / 100) * 360
        const nextAngle = currentAngle + angle

        const color = colors[index % colors.length]
        gradientStops.push(`${color} ${currentAngle}deg ${nextAngle}deg`)
        currentAngle = nextAngle
      })

      return `background: conic-gradient(from 0deg, ${gradientStops.join(', ')});`
    } catch (error) {
      console.error('计算饼图样式失败:', error)
      return 'background: #f0f0f0;'
    }
  },

  /**
   * 处理日历日期点击
   */
  onCalendarDayTap: function(event) {
    const { date } = event.currentTarget.dataset
    const calendarData = this.data.statistics.calendarHeatmap.calendarData
    const dayData = calendarData.find(item => item.dateKey === date)

    if (dayData && dayData.isInRange) {
      if (dayData.hasData) {
        const currentType = this.data.currentHeatmapType
        let content = `${dayData.year}年${dayData.month}月${dayData.day}日\n`

        switch (currentType) {
          case 'income':
            content += `收入：${this.data.currencySymbol}${dayData.incomeText}\n工作时长：${dayData.workTimeText}`
            break
          case 'workTime':
            content += `工作时长：${dayData.workTimeText}\n收入：${this.data.currencySymbol}${dayData.incomeText}`
            break
          case 'fishTime':
            content += `摸鱼时长：${dayData.fishingTimeText}\n工作时长：${dayData.workTimeText}`
            break
          case 'hourlyRate':
            content += `平均时薪：${this.data.currencySymbol}${dayData.hourlyRateText}\n工作时长：${dayData.workTimeText}\n收入：${this.data.currencySymbol}${dayData.incomeText}`
            break
        }

        wx.showModal({
          title: '当日详情',
          content: content,
          showCancel: false,
          confirmText: '确定'
        })
      } else {
        wx.showToast({
          title: '当日无工作记录',
          icon: 'none',
          duration: 1500
        })
      }
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    console.log('下拉刷新统计数据')

    // 清除缓存
    if (this.statisticsService) {
      this.statisticsService.clearCache()
    }

    // 重新加载数据
    this.loadStatistics()

    // 停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 处理页面分享
   */
  onShareAppMessage: function() {
    return {
      title: '我的工作统计数据',
      path: '/pages/statistics/index'
    }
  }
})
