<!--统计页面结构-->
<view class="statistics-page">
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载统计数据...</text>
      <view class="loading-skeleton">
        <view class="skeleton-card"></view>
        <view class="skeleton-card"></view>
        <view class="skeleton-card"></view>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{hasError}}" class="error-container">
    <view class="error-content">
      <view class="error-icon">⚠️</view>
      <text class="error-message">{{errorMessage}}</text>
      <button class="retry-btn" bindtap="onRefresh">重试</button>
    </view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    

    <!-- 时间范围选择器 -->
    <view class="time-range-section">
      <view class="section-header">
        <view class="section-title">📊 时间范围</view>
        <view class="section-subtitle">选择时间范围查看详细数据</view>
      </view>
      <view class="time-range-selector">
        <scroll-view class="range-options" scroll-x="true" enable-flex="true">
          <view class="range-option {{item.active ? 'active' : ''}}"
                wx:for="{{timeRangeOptions}}"
                wx:key="key"
                data-range="{{item.key}}"
                bindtap="onTimeRangeChange">
            <text class="option-text">{{item.text}}</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 工作履历选择区域（与时间范围更关联，提前放置） -->
    <view class="work-selector-section" wx:if="{{workHistories.length > 1}}">
      <view class="selector-header" bindtap="toggleWorkSelector">
        <view class="selector-title">
          <text class="title-icon">👥</text>
          <text class="title-text">工作履历选择</text>
        </view>
        <view class="selector-toggle">
          <text class="selected-count">已选择 {{selectedWorkIds.length}} 个履历</text>
          <text class="toggle-icon">{{showWorkSelector ? '▲' : '▼'}}</text>
        </view>
      </view>
      <view class="work-selector {{showWorkSelector ? 'show' : 'hide'}}">
        <view class="work-list">
          <view class="work-item {{item.selected ? 'selected' : ''}}"
                wx:for="{{workHistories}}"
                wx:key="id"
                data-work-id="{{item.id}}"
                bindtap="onWorkHistoryToggle">
            <view class="work-checkbox">
              <text class="checkbox-icon">{{item.selected ? '✓' : '○'}}</text>
            </view>
            <view class="work-info">
              <view class="work-name">{{item.displayName}}</view>
              <view class="work-period">
                {{item.startDate}} - {{item.endDate || '至今'}}
                <text class="current-tag" wx:if="{{item.isCurrent}}">当前</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速概览卡片 -->
    <view class="quick-overview">
      <view class="overview-cards-row">
        <view class="overview-card-small primary">
          <view class="card-icon">💰</view>
          <view class="card-content">
            <view class="card-value">{{currencySymbol}}{{statistics.overview.totalIncomeText}}</view>
            <view class="card-label">总收入</view>
          </view>
        </view>
        <view class="overview-card-small secondary">
          <view class="card-icon">⏰</view>
          <view class="card-content">
            <view class="card-value">{{statistics.overview.totalWorkTimeText}}</view>
            <view class="card-label">工作时长</view>
          </view>
        </view>
        <view class="overview-card-small accent">
          <view class="card-icon">📅</view>
          <view class="card-content">
            <view class="card-value">{{statistics.overview.workDays}}</view>
            <view class="card-label">工作天数</view>
          </view>
        </view>
        <view class="overview-card-small success">
          <view class="card-icon">⚡</view>
          <view class="card-content">
            <view class="card-value">{{currencySymbol}}{{statistics.overview.averageHourlyRateText}}</view>
            <view class="card-label">平均时薪</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 收入分解统计 -->
    <view class="income-breakdown-section" wx:if="{{statistics.incomeBreakdown}}">
      <view class="chart-card">
        <view class="card-header">
          <view class="card-title">💰 收入分解分析</view>
          <view class="card-subtitle">详细收入构成统计</view>
        </view>
        <view class="breakdown-cards">
          <view class="breakdown-card base">
            <view class="card-icon">⏰</view>
            <view class="card-content">
              <view class="card-value">{{currencySymbol}}{{statistics.incomeBreakdown.baseIncomeText}}</view>
              <view class="card-label">基础收入</view>
              <view class="card-subtitle">时间段工作收入</view>
            </view>
          </view>

          <view class="breakdown-card positive" wx:if="{{statistics.incomeBreakdown.extraIncome > 0}}">
            <view class="card-icon">📈</view>
            <view class="card-content">
              <view class="card-value">+{{currencySymbol}}{{statistics.incomeBreakdown.extraIncomeText}}</view>
              <view class="card-label">额外收入</view>
              <view class="card-subtitle">提成、奖金、补贴等</view>
            </view>
          </view>

          <view class="breakdown-card negative" wx:if="{{statistics.incomeBreakdown.deductions > 0}}">
            <view class="card-icon">📉</view>
            <view class="card-content">
              <view class="card-value">-{{currencySymbol}}{{statistics.incomeBreakdown.deductionsText}}</view>
              <view class="card-label">扣款</view>
              <view class="card-subtitle">请假、迟到、违规等</view>
            </view>
          </view>

          <view class="breakdown-card summary" wx:if="{{statistics.incomeBreakdown.hasAdjustments}}">
            <view class="card-icon">💰</view>
            <view class="card-content">
              <view class="card-value {{statistics.incomeBreakdown.adjustmentBalance >= 0 ? 'positive' : 'negative'}}">
                {{statistics.incomeBreakdown.adjustmentBalance >= 0 ? '+' : ''}}{{currencySymbol}}{{statistics.incomeBreakdown.adjustmentBalanceText}}
              </view>
              <view class="card-label">调整结余</view>
              <view class="card-subtitle">额外收入 - 扣款</view>
            </view>
          </view>
        </view>

        <!-- 收入调整详细统计 -->
        <view class="adjustment-details" wx:if="{{statistics.incomeBreakdown.hasAdjustments}}">
          <!-- 额外收入按类型统计 -->
          <view class="detail-section" wx:if="{{statistics.incomeBreakdown.extraIncomeByType && Object.keys(statistics.incomeBreakdown.extraIncomeByType).length > 0}}">
            <view class="detail-title">额外收入明细</view>
            <view class="type-stats">
              <view class="type-item" wx:for="{{statistics.incomeBreakdown.extraIncomeTypesList}}" wx:key="type">
                <view class="type-info">
                  <view class="type-name">{{item.typeName}}</view>
                  <view class="type-count">{{item.count}}次</view>
                </view>
                <view class="type-amount positive">+{{currencySymbol}}{{item.amountText}}</view>
              </view>
            </view>
          </view>

          <!-- 扣款按类型统计 -->
          <view class="detail-section" wx:if="{{statistics.incomeBreakdown.deductionsByType && Object.keys(statistics.incomeBreakdown.deductionsByType).length > 0}}">
            <view class="detail-title">扣款明细</view>
            <view class="type-stats">
              <view class="type-item" wx:for="{{statistics.incomeBreakdown.deductionsTypesList}}" wx:key="type">
                <view class="type-info">
                  <view class="type-name">{{item.typeName}}</view>
                  <view class="type-count">{{item.count}}次</view>
                </view>
                <view class="type-amount negative">-{{currencySymbol}}{{item.amountText}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 收入调整统计 -->
    <view class="income-adjustments-section" wx:if="{{statistics.incomeAdjustments.hasAdjustments}}">
      <view class="chart-card">
        <view class="card-header">
          <view class="card-title">💰 收入调整统计</view>
          <view class="card-subtitle">额外收入与扣款分析</view>
        </view>
        <view class="adjustments-overview">
          <view class="adjustment-item extra">
            <view class="adjustment-icon">📈</view>
            <view class="adjustment-content">
              <view class="adjustment-value">+{{currencySymbol}}{{statistics.incomeAdjustments.totalExtraIncome}}</view>
              <view class="adjustment-label">额外收入 ({{statistics.incomeAdjustments.extraIncomeCount}}次)</view>
            </view>
          </view>
          <view class="adjustment-item deduction">
            <view class="adjustment-icon">📉</view>
            <view class="adjustment-content">
              <view class="adjustment-value">-{{currencySymbol}}{{statistics.incomeAdjustments.totalDeductions}}</view>
              <view class="adjustment-label">扣款 ({{statistics.incomeAdjustments.deductionsCount}}次)</view>
            </view>
          </view>
          <view class="adjustment-item net {{statistics.incomeAdjustments.netAdjustment >= 0 ? 'positive' : 'negative'}}">
            <view class="adjustment-icon">💰</view>
            <view class="adjustment-content">
              <view class="adjustment-value">{{statistics.incomeAdjustments.netAdjustment >= 0 ? '+' : ''}}{{currencySymbol}}{{statistics.incomeAdjustments.netAdjustment}}</view>
              <view class="adjustment-label">净调整</view>
            </view>
          </view>
        </view>

        <!-- 收入类型分布饼图 -->
        <view class="pie-charts-container" wx:if="{{statistics.incomeAdjustments.hasExtraIncomeTypes || statistics.incomeAdjustments.hasDeductionTypes}}">
          <!-- 额外收入类型分布 -->
          <view class="pie-chart-section" wx:if="{{statistics.incomeAdjustments.hasExtraIncomeTypes}}">
            <view class="pie-section-title">📈 额外收入类型分布</view>
            <view class="chart-content two-col">
              <view class="pie-chart-container small">
                <view class="pie-chart small" style="{{statistics.incomeAdjustments.extraIncomePieStyle}}">
                  <view class="pie-center small">
                    <view class="pie-total small">{{currencySymbol}}{{statistics.incomeAdjustments.totalExtraIncome}}</view>
                    <view class="pie-label small">总额</view>
                  </view>
                </view>
              </view>
              <view class="chart-legend-compact">
                <view class="legend-item-compact" wx:for="{{statistics.incomeAdjustments.extraIncomeSegments}}" wx:key="type">
              <view class="legend-dot small" style="background: {{item.color}};"></view>
                  <view class="legend-text-compact">
                    <text class="legend-name-compact">{{item.label}}</text>
                    <text class="legend-value-compact">{{item.percentage}}% · {{currencySymbol}}{{item.amount}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 扣款类型分布 -->
          <view class="pie-chart-section" wx:if="{{statistics.incomeAdjustments.hasDeductionTypes}}">
            <view class="pie-section-title">📉 扣款类型分布</view>
            <view class="chart-content two-col">
              <view class="pie-chart-container small">
                <view class="pie-chart small" style="{{statistics.incomeAdjustments.deductionPieStyle}}">
                  <view class="pie-center small">
                    <view class="pie-total small">{{currencySymbol}}{{statistics.incomeAdjustments.totalDeductions}}</view>
                    <view class="pie-label small">总额</view>
                  </view>
                </view>
              </view>
              <view class="chart-legend-compact">
                <view class="legend-item-compact" wx:for="{{statistics.incomeAdjustments.deductionSegments}}" wx:key="type">
                  <view class="legend-dot small" style="background: {{item.color}};"></view>
                  <view class="legend-text-compact">
                    <text class="legend-name-compact">{{item.label}}</text>
                    <text class="legend-value-compact">{{item.percentage}}% · {{currencySymbol}}{{item.amount}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 日期类型分布分析 -->
    <view class="date-type-section" wx:if="{{statistics.dateTypeDistribution.totalDays > 0}}">
      <view class="chart-card">
        <view class="card-header">
          <view class="card-title">📅 日期类型分布</view>
          <view class="card-subtitle">工作日、周末、节假日分布</view>
        </view>
        <view class="chart-content two-col">
          <view class="pie-chart-container">
            <view class="pie-chart" style="{{statistics.dateTypeDistribution.pieChartStyle}}">
              <view class="pie-center">
                <view class="pie-total">{{statistics.dateTypeDistribution.totalDays}}</view>
                <view class="pie-label">总天数</view>
              </view>
            </view>
          </view>
          <view class="chart-legend">
            <view class="legend-item-inline" wx:for="{{statistics.dateTypeDistribution.segments}}" wx:key="type">
              <view class="legend-dot" style="background: {{item.color}};"></view>
              <view class="legend-content-inline">
                <view class="legend-name-inline">{{item.label}}</view>
                <view class="legend-value-inline">{{item.percentage}}% · {{item.count}}天</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 其他统计数据 -->
    <view class="other-stats-section">
      <!-- 摸鱼统计 -->
      <view class="modern-stat-card fishing-card" wx:if="{{statistics.fishing.fishingCount > 0}}">
        <view class="modern-card-left">
          <view class="modern-card-icon">🎣</view>
          <view class="modern-title-section">
            <view class="modern-card-title">摸鱼统计</view>
            <view class="modern-card-subtitle">工作期间的休息时间</view>
          </view>
        </view>
        <view class="modern-card-right">
          <view class="modern-stats-row">
            <view class="modern-stat-item">
              <view class="modern-stat-value">{{statistics.fishing.totalFishingTimeText}}</view>
              <view class="modern-stat-label">总摸鱼时长</view>
            </view>
            <view class="modern-stat-item">
              <view class="modern-stat-value">{{statistics.fishing.fishingCount}}</view>
              <view class="modern-stat-label">摸鱼次数</view>
            </view>
            <view class="modern-stat-item">
              <view class="modern-stat-value">{{statistics.fishing.averageFishingDurationText}}</view>
              <view class="modern-stat-label">平均时长</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 时薪统计 -->
      <view class="modern-stat-card hourly-card" wx:if="{{statistics.hourlyRate.count > 0}}">
        <view class="modern-card-left">
          <view class="modern-card-icon">💵</view>
          <view class="modern-title-section">
            <view class="modern-card-title">时薪统计</view>
            <view class="modern-card-subtitle">工作效率分析</view>
          </view>
        </view>
        <view class="modern-card-right">
          <view class="modern-stats-row">
            <view class="modern-stat-item">
              <view class="modern-stat-value">{{currencySymbol}}{{statistics.hourlyRate.averageText}}</view>
              <view class="modern-stat-label">平均时薪</view>
            </view>
            <view class="modern-stat-item">
              <view class="modern-stat-value">{{currencySymbol}}{{statistics.hourlyRate.maxText}}</view>
              <view class="modern-stat-label">最高时薪</view>
            </view>
            <view class="modern-stat-item">
              <view class="modern-stat-value">{{currencySymbol}}{{statistics.hourlyRate.minText}}</view>
              <view class="modern-stat-label">最低时薪</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 工作履历统计 -->
      <view class="modern-stat-card history-card" wx:if="{{statistics.workHistory.totalWorks > 0}}">
        <view class="modern-card-left">
          <view class="modern-card-icon">📋</view>
          <view class="modern-title-section">
            <view class="modern-card-title">工作履历</view>
            <view class="modern-card-subtitle">职业发展概况</view>
          </view>
        </view>
        <view class="modern-card-right">
          <view class="modern-stats-row">
            <view class="modern-stat-item">
              <view class="modern-stat-value">{{statistics.workHistory.totalWorks}}</view>
              <view class="modern-stat-label">总工作数</view>
            </view>
            <view class="modern-stat-item">
              <view class="modern-stat-value">{{statistics.workHistory.maxWorkDays}}</view>
              <view class="modern-stat-label">最长在职</view>
            </view>
            <view class="modern-stat-item">
              <view class="modern-stat-value">{{statistics.workHistory.averageWorkDays}}</view>
              <view class="modern-stat-label">平均在职天数</view>
            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 工作节奏分析 -->
    <view class="analysis-section" wx:if="{{statistics.workRhythmAnalysis && statistics.workRhythmAnalysis.totalDays > 0}}">
      <view class="section-title">⏱️ 工作节奏分析</view>
      <view class="rhythm-container">
        <view class="rhythm-description">最近时段的主要工作时段偏好</view>
        <view class="rhythm-chart">
          <view class="rhythm-item">
            <view class="rhythm-icon">🌅</view>
            <view class="rhythm-content">
              <view class="rhythm-label">早起型</view>
              <view class="rhythm-bar"><view class="rhythm-fill morning" style="width: {{statistics.workRhythmAnalysis.morningPersonPercentage}}%"></view></view>
              <view class="rhythm-percentage">{{statistics.workRhythmAnalysis.morningPersonPercentage}}%</view>
            </view>
          </view>
          <view class="rhythm-item">
            <view class="rhythm-icon">🌞</view>
            <view class="rhythm-content">
              <view class="rhythm-label">下午型</view>
              <view class="rhythm-bar"><view class="rhythm-fill afternoon" style="width: {{statistics.workRhythmAnalysis.afternoonPersonPercentage}}%"></view></view>
              <view class="rhythm-percentage">{{statistics.workRhythmAnalysis.afternoonPersonPercentage}}%</view>
            </view>
          </view>
          <view class="rhythm-item">
            <view class="rhythm-icon">🌙</view>
            <view class="rhythm-content">
              <view class="rhythm-label">夜猫子</view>
              <view class="rhythm-bar"><view class="rhythm-fill night" style="width: {{statistics.workRhythmAnalysis.nightOwlPercentage}}%"></view></view>
              <view class="rhythm-percentage">{{statistics.workRhythmAnalysis.nightOwlPercentage}}%</view>
            </view>
          </view>
          <view class="rhythm-item">
            <view class="rhythm-icon">🕘</view>
            <view class="rhythm-content">
              <view class="rhythm-label">全天型</view>
              <view class="rhythm-bar"><view class="rhythm-fill allday" style="width: {{statistics.workRhythmAnalysis.allDayPercentage}}%"></view></view>
              <view class="rhythm-percentage">{{statistics.workRhythmAnalysis.allDayPercentage}}%</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 工作模式分析 -->
    <view class="analysis-section" wx:if="{{statistics.workPatternAnalysis && statistics.workPatternAnalysis.hourlyDistribution && statistics.workPatternAnalysis.hourlyDistribution.length > 0}}">
      <view class="section-title">🧭 工作模式分析</view>
      <view class="chart-container">
        <!-- 小时分布 -->
        <view class="efficiency-chart">
          <view class="chart-label">小时分布（全天）</view>
          <view class="chart-item" wx:for="{{statistics.workPatternAnalysis.hourlyDistribution}}" wx:key="hour">
            <view class="chart-bar-container">
              <view class="chart-bar bar-green" style="width: {{item.styleWidth}};"></view>
              <text class="chart-value">{{item.timeLabel}} · {{item.workTimeText}}</text>
            </view>
          </view>
        </view>

        <!-- 工作日分布 -->
        <view class="efficiency-chart">
          <view class="chart-label">工作日分布（周内）</view>
          <view class="chart-item" wx:for="{{statistics.workPatternAnalysis.weekdayDistribution}}" wx:key="weekday">
            <view class="chart-bar-container">
              <view class="chart-bar bar-blue" style="width: {{item.styleWidth}};"></view>
              <text class="chart-value">{{item.weekdayLabel}} · {{item.workTimeText}} · {{currencySymbol}}{{item.incomeText}} · 平均 {{item.avgWorkTimeText}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 效率分布 -->
    <view class="analysis-section" wx:if="{{statistics.efficiencyDistribution && statistics.efficiencyDistribution.ranges && statistics.efficiencyDistribution.ranges.length > 0}}">
      <view class="section-title">⚙️ 效率分布</view>
      <view class="efficiency-chart">
        <view class="chart-description">按日均时薪分布</view>
          <view class="chart-item" wx:for="{{statistics.efficiencyDistribution.ranges}}" wx:key="label">
          <view class="chart-label">{{item.label}}（{{item.count}}天）</view>
          <view class="chart-bar-container">
            <view class="chart-bar bar-teal" style="width: {{item.styleWidth}};"></view>
            <text class="chart-value">{{item.percentage}}%</text>
          </view>
          <view class="source-details">
            <view class="source-detail"><text class="detail-label">时长</text><text class="detail-value">{{item.totalHoursText}}</text></view>
            <view class="source-detail"><text class="detail-label">收入</text><text class="detail-value">{{currencySymbol}}{{item.totalIncomeText}}</text></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 趋势分析 -->
    <view class="analysis-section" wx:if="{{statistics.trendAnalysis && statistics.trendAnalysis.dailyStats && statistics.trendAnalysis.dailyStats.length > 0}}">
      <view class="section-title">📉 趋势分析</view>
      <view class="trend-container">
        <view class="trend-item">
          <view class="trend-icon">⏱️</view>
          <view class="trend-content">
            <view class="trend-label">工作时长</view>
            <view class="trend-value">{{statistics.trendAnalysis.averages.workHoursText}}</view>
            <view class="trend-change {{statistics.trendAnalysis.trends.workTimeDirection}}">{{statistics.trendAnalysis.trends.workTimeText}}</view>
          </view>
        </view>
        <view class="trend-item">
          <view class="trend-icon">💰</view>
          <view class="trend-content">
            <view class="trend-label">收入</view>
            <view class="trend-value">{{currencySymbol}}{{statistics.trendAnalysis.averages.incomeText}}</view>
            <view class="trend-change {{statistics.trendAnalysis.trends.incomeDirection}}">{{statistics.trendAnalysis.trends.incomeText}}</view>
          </view>
        </view>
        <view class="trend-item">
          <view class="trend-icon">⚡</view>
          <view class="trend-content">
            <view class="trend-label">效率（时薪）</view>
            <view class="trend-value">{{currencySymbol}}{{statistics.trendAnalysis.averages.efficiencyText}}</view>
            <view class="trend-change {{statistics.trendAnalysis.trends.efficiencyDirection}}">{{statistics.trendAnalysis.trends.efficiencyText}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 对比分析卡片 -->
    <view class="analysis-section" wx:if="{{statistics.comparisonAnalysis}}">
      <view class="section-title">📊 环比分析</view>
      <view class="comparison-container">
        <view class="comparison-item">
          <view class="comparison-label">工作时长</view>
          <view class="comparison-values">
            <view class="current-value">{{statistics.comparisonAnalysis.current.totalWorkHours}}h</view>
            <view class="change-indicator {{statistics.comparisonAnalysis.changes.workHoursDirection}}">
              {{statistics.comparisonAnalysis.changes.workHoursText}}
            </view>
          </view>
        </view>

        <view class="comparison-item">
          <view class="comparison-label">总收入</view>
          <view class="comparison-values">
            <view class="current-value">{{currencySymbol}}{{statistics.comparisonAnalysis.current.totalIncome}}</view>
            <view class="change-indicator {{statistics.comparisonAnalysis.changes.incomeDirection}}">
              {{statistics.comparisonAnalysis.changes.incomeText}}
            </view>
          </view>
        </view>

        <view class="comparison-item">
          <view class="comparison-label">平均时薪</view>
          <view class="comparison-values">
            <view class="current-value">{{currencySymbol}}{{statistics.comparisonAnalysis.current.averageHourlyRate}}</view>
            <view class="change-indicator {{statistics.comparisonAnalysis.changes.hourlyRateDirection}}">
              {{statistics.comparisonAnalysis.changes.hourlyRateText}}
            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 数据可视化区域 -->
    <view class="visualization-section">
      <!-- 收入来源分析 -->
      <view class="chart-card" wx:if="{{statistics.incomeSourceAnalysis.sources.length > 0}}">
        <view class="card-header">
          <view class="card-title">💰 收入来源分析</view>
          <view class="card-subtitle">各收入来源占比分布</view>
        </view>
        <view class="chart-content two-col">
          <view class="pie-chart-container">
            <view class="pie-chart" style="{{statistics.incomeSourceAnalysis.pieChartStyle}}">
              <view class="pie-center">
                <view class="pie-total">{{currencySymbol}}{{statistics.incomeSourceAnalysis.totalIncomeText}}</view>
                <view class="pie-label">总收入</view>
              </view>
            </view>
          </view>
          <view class="chart-legend">
            <view class="legend-item" wx:for="{{statistics.incomeSourceAnalysis.sources}}" wx:key="name" wx:if="{{index < 5}}">
              <view class="legend-dot" style="background: {{item.color}};"></view>
              <view class="legend-content">
                <view class="legend-name">{{item.name}}</view>
                <view class="legend-value">{{item.percentage}}% · {{currencySymbol}}{{item.totalIncomeText}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 时间分布分析 -->
      <view class="chart-card" wx:if="{{statistics.timeDistributionPie.segments.length > 0}}">
        <view class="card-header">
          <view class="card-title">⏰ 时间分布分析</view>
          <view class="card-subtitle">工作时间类型分布</view>
        </view>
        <view class="chart-content two-col">
          <view class="pie-chart-container">
            <view class="pie-chart" style="{{statistics.timeDistributionPie.pieChartStyle}}">
              <view class="pie-center">
                <view class="pie-total">{{statistics.timeDistributionPie.totalHoursText}}</view>
                <view class="pie-label">总时长</view>
              </view>
            </view>
          </view>
          <view class="chart-legend">
            <view class="legend-item-inline" wx:for="{{statistics.timeDistributionPie.segments}}" wx:key="type">
              <view class="legend-dot" style="background: {{item.color}};"></view>
              <view class="legend-content-inline">
                <view class="legend-name-inline">{{item.label}}</view>
                <view class="legend-value-inline">{{item.percentage}}% · {{item.hoursText}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 排行榜卡片 -->
    <view class="analysis-section" wx:if="{{statistics.rankingAnalysis}}">
      <view class="section-title">🏆 最佳表现</view>
      <view class="ranking-container">
        <!-- 最高收入 -->
        <view class="ranking-category" wx:if="{{statistics.rankingAnalysis.topIncome.length > 0}}">
          <view class="category-title">💰 最高收入日</view>
          <view class="ranking-list">
            <view class="ranking-item" wx:for="{{statistics.rankingAnalysis.topIncome}}" wx:key="date" wx:if="{{index < 3}}">
              <view class="rank-number">{{item.rank}}</view>
              <view class="rank-content">
                <view class="rank-date">{{item.dateText}}</view>
                <view class="rank-value">{{currencySymbol}}{{item.incomeText}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 最长工作时间 -->
        <view class="ranking-category" wx:if="{{statistics.rankingAnalysis.topWorkHours.length > 0}}">
          <view class="category-title">⏰ 最长工作日</view>
          <view class="ranking-list">
            <view class="ranking-item" wx:for="{{statistics.rankingAnalysis.topWorkHours}}" wx:key="date" wx:if="{{index < 3}}">
              <view class="rank-number">{{item.rank}}</view>
              <view class="rank-content">
                <view class="rank-date">{{item.dateText}}</view>
                <view class="rank-value">{{item.workHoursText}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 收入增长趋势 -->
    <view class="analysis-section" wx:if="{{statistics.incomeGrowthTrend && statistics.incomeGrowthTrend.trendData && statistics.incomeGrowthTrend.trendData.length > 0}}">
      <view class="section-title">📈 收入增长趋势</view>
      <view class="growth-container">
        <view class="growth-summary">
          <view class="summary-item">
            <view class="summary-value">{{statistics.incomeGrowthTrend.totalMonths}}</view>
            <view class="summary-label">统计月数</view>
          </view>
          <view class="summary-item">
            <view class="summary-value">{{currencySymbol}}{{statistics.incomeGrowthTrend.avgMonthlyIncome}}</view>
            <view class="summary-label">月均收入</view>
          </view>
        </view>

        <view class="growth-chart">
          <view class="growth-item" wx:for="{{statistics.incomeGrowthTrend.trendData}}" wx:key="month">
            <view class="growth-month">{{item.monthName}}</view>
            <view class="growth-bar-container">
              <view class="growth-bar growth-bar-fill" style="height: {{item.barHeight}}px;"></view>
            </view>
            <view class="growth-value">{{currencySymbol}}{{item.income}}</view>
            <view class="growth-change {{item.growthDirection}}" wx:if="{{item.growth !== 0}}">
              {{item.growth > 0 ? '+' : ''}}{{item.growth}}%
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 日历热力图卡片 -->
    <view class="analysis-section" wx:if="{{statistics.calendarHeatmap.calendarData}}">
      <view class="heatmap-header">
        <view class="section-title">📅 数据热力图</view>
        <view class="heatmap-type-selector">
          <view
            class="type-btn {{item.active ? 'active' : ''}} {{item.color}}"
            wx:for="{{heatmapTypes}}"
            wx:key="key"
            data-type="{{item.key}}"
            bindtap="onHeatmapTypeChange">
            {{item.text}}
          </view>
        </view>
      </view>

      <view class="heatmap-container">
        <!-- 日历信息 -->
        <view class="calendar-info">
          <text class="calendar-period">{{statistics.calendarHeatmap.dateRangeText}}</text>
          <text class="calendar-stats">{{statistics.calendarHeatmap.workDays}} 个工作日</text>
        </view>

        <!-- 日历热力图网格 -->
        <view class="calendar-heatmap">
          <!-- 星期标题 -->
          <view class="calendar-weekdays">
            <view class="weekday-label">日</view>
            <view class="weekday-label">一</view>
            <view class="weekday-label">二</view>
            <view class="weekday-label">三</view>
            <view class="weekday-label">四</view>
            <view class="weekday-label">五</view>
            <view class="weekday-label">六</view>
          </view>

          <!-- 日历网格 -->
          <view class="calendar-grid">
            <view class="calendar-week" wx:for="{{statistics.calendarHeatmap.weeks}}" wx:key="index">
              <view
                class="calendar-day {{day.colorClass}} {{!day.isInRange ? 'out-of-range' : ''}}"
                wx:for="{{item}}"
                wx:for-item="day"
                wx:key="dateKey"
                data-date="{{day.dateKey}}"
                bindtap="onCalendarDayTap">
                <!-- 月份标签 -->
                <view class="month-label" wx:if="{{day.day === 1 && day.isInRange}}">{{day.month}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 热力图图例 -->
        <view class="heatmap-legend">
          <text class="legend-label">强度：</text>
          <view class="legend-colors">
            <view class="legend-item">
              <view class="legend-color heat-none"></view>
              <text class="legend-text">无</text>
            </view>
            <view class="legend-item">
              <view class="legend-color heat-green-very-low" wx:if="{{currentHeatmapType === 'income' || currentHeatmapType === 'hourlyRate'}}"></view>
              <view class="legend-color heat-red-very-low" wx:elif="{{currentHeatmapType === 'workTime'}}"></view>
              <view class="legend-color heat-gold-very-low" wx:elif="{{currentHeatmapType === 'fishTime'}}"></view>
              <view class="legend-color heat-blue-very-low" wx:else></view>
              <text class="legend-text">低</text>
            </view>
            <view class="legend-item">
              <view class="legend-color heat-green-medium" wx:if="{{currentHeatmapType === 'income' || currentHeatmapType === 'hourlyRate'}}"></view>
              <view class="legend-color heat-red-medium" wx:elif="{{currentHeatmapType === 'workTime'}}"></view>
              <view class="legend-color heat-gold-medium" wx:elif="{{currentHeatmapType === 'fishTime'}}"></view>
              <view class="legend-color heat-blue-medium" wx:else></view>
              <text class="legend-text">中</text>
            </view>
            <view class="legend-item">
              <view class="legend-color heat-green-high" wx:if="{{currentHeatmapType === 'income' || currentHeatmapType === 'hourlyRate'}}"></view>
              <view class="legend-color heat-red-high" wx:elif="{{currentHeatmapType === 'workTime'}}"></view>
              <view class="legend-color heat-gold-high" wx:elif="{{currentHeatmapType === 'fishTime'}}"></view>
              <view class="legend-color heat-blue-high" wx:else></view>
              <text class="legend-text">高</text>
            </view>
            <view class="legend-item">
              <view class="legend-color heat-green-very-high" wx:if="{{currentHeatmapType === 'income' || currentHeatmapType === 'hourlyRate'}}"></view>
              <view class="legend-color heat-red-very-high" wx:elif="{{currentHeatmapType === 'workTime'}}"></view>
              <view class="legend-color heat-gold-very-high" wx:elif="{{currentHeatmapType === 'fishTime'}}"></view>
              <view class="legend-color heat-blue-very-high" wx:else></view>
              <text class="legend-text">很高</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 历史总览区域 -->
    <view class="historical-section" wx:if="{{statistics.historical.allTime}}">
      <view class="chart-card">
        <view class="card-header">
          <view class="card-title">📈 历史总览</view>
          <view class="card-subtitle">全时段数据统计</view>
        </view>

        <!-- 总体数据 -->
        <view class="historical-overview">
          <view class="overview-stats-row">
            <view class="overview-item-small">
              <view class="overview-icon">⏰</view>
              <view class="overview-content">
                <view class="overview-value">{{statistics.historical.allTime.totalWorkTimeText}}</view>
                <view class="overview-label">总工作时长</view>
              </view>
            </view>

            <view class="overview-item-small">
              <view class="overview-icon">💰</view>
              <view class="overview-content">
                <view class="overview-value">{{currencySymbol}}{{statistics.historical.allTime.totalIncomeText}}</view>
                <view class="overview-label">总收入</view>
              </view>
            </view>

            <view class="overview-item-small">
              <view class="overview-icon">📅</view>
              <view class="overview-content">
                <view class="overview-value">{{statistics.historical.allTime.workDays}}</view>
                <view class="overview-label">工作天数</view>
              </view>
            </view>

            <view class="overview-item-small">
              <view class="overview-icon">⚡</view>
              <view class="overview-content">
                <view class="overview-value">{{currencySymbol}}{{statistics.historical.allTime.averageHourlyRateText}}</view>
                <view class="overview-label">平均时薪</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 历史最佳记录 -->
        <view class="best-records" wx:if="{{statistics.historical.bestRecords}}">
          <view class="records-title">🏆 历史最佳</view>
          <view class="records-grid">
            <view class="record-item" wx:if="{{statistics.historical.bestRecords.highestIncome.amount > 0}}">
              <view class="record-icon">💰</view>
              <view class="record-content">
                <view class="record-value">{{currencySymbol}}{{statistics.historical.bestRecords.highestIncome.amountText}}</view>
                <view class="record-label">最高单日收入</view>
                <view class="record-date">{{statistics.historical.bestRecords.highestIncome.dateText}}</view>
              </view>
            </view>

            <view class="record-item" wx:if="{{statistics.historical.bestRecords.longestWork.minutes > 0}}">
              <view class="record-icon">⏰</view>
              <view class="record-content">
                <view class="record-value">{{statistics.historical.bestRecords.longestWork.timeText}}</view>
                <view class="record-label">最长工作时间</view>
                <view class="record-date">{{statistics.historical.bestRecords.longestWork.dateText}}</view>
              </view>
            </view>

            <view class="record-item" wx:if="{{statistics.historical.bestRecords.bestEfficiency.rate > 0}}">
              <view class="record-icon">⚡</view>
              <view class="record-content">
                <view class="record-value">{{currencySymbol}}{{statistics.historical.bestRecords.bestEfficiency.rateText}}</view>
                <view class="record-label">最高时薪</view>
                <view class="record-date">{{statistics.historical.bestRecords.bestEfficiency.dateText}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </view>

  <!-- 自定义日期选择器 -->
  <date-range-picker
    visible="{{showDatePicker}}"
    defaultStartDate="{{customStartDate}}"
    defaultEndDate="{{customEndDate}}"
    bind:confirm="onDateRangeConfirm"
    bind:cancel="onDateRangeCancel">
  </date-range-picker>
</view>
