// 批量操作模态框组件
const BatchCopyService = require('../../core/services/batch-copy-service.js')
const { CurrencyUtils } = require('../../utils/currency-utils.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 操作类型：'import' 或 'copy'
    operation: {
      type: String,
      value: 'copy'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 批量复制步骤：1-选择源日期，2-选择目标日期
    batchStep: 1,

    // 日历相关
    batchCalendarYear: new Date().getFullYear(),
    batchCalendarMonth: new Date().getMonth() + 1,
    batchCalendarDays: [],

    // 导入模式相关
    templateDates: [],
    selectedTemplate: null,

    // 批量复制相关
    selectedSourceDate: null,
    selectedSourceDateObject: null,
    sourceSchedulePreview: null,
    selectedTargetDates: [],

    // 复制选项 - 始终复制状态和时间段
    copyStatus: true,
    copySegments: true,

    // 快速选择相关
    currentMonthWorkdays: 0,
    currentMonthSelectedWorkdays: 0,
    isHolidayLoading: false,

    // 数据冲突检测
    hasDataConflict: false,
    conflictDatesCount: 0,

    // 批量复制状态
    batchCopyInProgress: false,
    batchCopyCompleted: false,

    // 滚动提示
    showScrollIndicator: false,
    hasShownScrollIndicator: false,

    // 模态框动画状态
    modalVisible: false,

    // 货币设置
    currencySymbol: '¥',

    // 组件内部数据
    currentDate: '',
    currentWorkId: '',

    // 源数据分析
    sourceDataTypes: null,

    // 选择的日期范围显示
    selectedDateRange: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化批量复制服务
     */
    _initializeBatchCopyService() {
      if (!this.batchCopyService) {
        const timeSegmentService = this._getTimeSegmentService()
        if (timeSegmentService) {
          this.batchCopyService = new BatchCopyService(timeSegmentService)
        }
      }
    },

    /**
     * 监听属性变化
     */
    _onPropertiesChange() {
      if (this.properties.visible) {
        // 初始化模态框
        this._initializeModal()
      } else {
        this.setData({ modalVisible: false })
      }
    },

    /**
     * 初始化模态框
     */
    _initializeModal() {
      const now = new Date()

      // 加载货币设置
      this._loadCurrencySettings()

      // 初始化服务
      this._initializeBatchCopyService()

      // 加载组件数据
      this._loadComponentData()

      // 重置状态
      this.setData({
        batchStep: 1,
        batchCalendarYear: now.getFullYear(),
        batchCalendarMonth: now.getMonth() + 1,
        selectedSourceDate: null,
        selectedSourceDateObject: null,
        sourceSchedulePreview: null,
        selectedTargetDates: [],
        selectedTemplate: null,
        copyStatus: true,
        copySegments: true,
        currentMonthWorkdays: 0,
        currentMonthSelectedWorkdays: 0,
        isHolidayLoading: false,
        hasDataConflict: false,
        conflictDatesCount: 0,
        batchCopyInProgress: false,
        batchCopyCompleted: false,
        showScrollIndicator: false,
        hasShownScrollIndicator: false,
        sourceDataTypes: null
      }, () => {
        // 根据操作类型初始化
        if (this.properties.operation === 'import') {
          this._initializeImportMode()
        } else {
          this._initializeCopyMode()
        }

        // 延迟显示动画
        setTimeout(() => {
          this.setData({ modalVisible: true })
        }, 50)
      })
    },

    /**
     * 加载货币设置
     */
    _loadCurrencySettings() {
      try {
        CurrencyUtils.loadCurrencySettingsForContext(this, {
          includeSymbol: true
        })
      } catch (error) {
        console.error('加载货币设置失败:', error)
        this.setData({
          currencySymbol: '¥'
        })
      }
    },

    /**
     * 初始化导入模式
     */
    _initializeImportMode() {
      this._loadTemplateDates()
    },

    /**
     * 初始化批量复制模式
     */
    _initializeCopyMode() {
      this.generateBatchCalendar()
    },

    /**
     * 获取时间段服务实例
     */
    _getTimeSegmentService() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (currentPage.timeSegmentService) {
          return currentPage.timeSegmentService
        }
      }
      return null
    },

    /**
     * 获取节假日管理器
     */
    _getHolidayManager() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (currentPage.holidayManager) {
          return currentPage.holidayManager
        }
      }
      return null
    },

    /**
     * 加载模板日期数据
     */
    _loadTemplateDates() {
      const timeSegmentService = this._getTimeSegmentService()
      if (!timeSegmentService) {
        console.warn('timeSegmentService not available')
        return
      }

      // 获取所有有数据的日期作为模板
      const templateDates = timeSegmentService.getDatesWithData()
        .map(date => {
          const dayData = timeSegmentService.getDayData(date)
          const status = timeSegmentService.getDateStatus(date, this.data.currentWorkId)
          const statusConfig = timeSegmentService.getDateStatusConfig(status)
          
          return {
            date,
            dateKey: date.toISOString(),
            dateText: this._formatDate(date),
            dailyIncome: dayData.dailyIncome || 0,
            segmentCount: dayData.segments.length,
            statusConfig: {
              ...statusConfig,
              name: statusConfig.name || '无状态'
            }
          }
        })
        .filter(item => item.segmentCount > 0)

      this.setData({
        templateDates
      })
    },

    /**
     * 生成批量操作日历
     */
    generateBatchCalendar() {
      const timeSegmentService = this._getTimeSegmentService()
      if (!timeSegmentService) {
        console.warn('timeSegmentService not available')
        return
      }

      const { batchCalendarYear, batchCalendarMonth } = this.data

      // 获取当月第一天和最后一天
      const firstDay = new Date(batchCalendarYear, batchCalendarMonth - 1, 1)
      const lastDay = new Date(batchCalendarYear, batchCalendarMonth, 0)

      // 获取当月天数
      const daysInMonth = lastDay.getDate()

      // 生成日历数据 - 只显示工作日和节假日
      const calendarDays = []
      let currentMonthWorkdays = 0
      let currentMonthSelectedWorkdays = 0

      // 添加当月的天数，但只显示工作日和节假日
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(batchCalendarYear, batchCalendarMonth - 1, day)
        const dayOfWeek = date.getDay()

        // 检查是否是节假日
        let isHoliday = false
        let holidayInfo = null
        const holidayManager = this._getHolidayManager()
        if (holidayManager) {
          holidayInfo = holidayManager.getDateInfo(date)
          isHoliday = holidayInfo && holidayInfo.type !== 'normal'
        }

        // 只显示工作日（周一到周五）或节假日
        const isWorkday = dayOfWeek >= 1 && dayOfWeek <= 5
        if (!isWorkday && !isHoliday) {
          continue // 跳过周六日（除非是节假日）
        }

        const dayData = timeSegmentService.getDayData(date)
        const status = timeSegmentService.getDateStatus(date, this.data.currentWorkId)
        const hasData = (dayData.segments && dayData.segments.length > 0) || !!status
        const statusConfig = timeSegmentService.getDateStatusConfig(status)

        // 检查是否被选中为目标日期
        const isSelectedTarget = this.data.selectedTargetDates.some(targetDate =>
          targetDate.toDateString() === date.toDateString()
        )

        // 检查是否是选中的源日期
        const isSelectedSource = this.data.selectedSourceDate &&
          this.data.selectedSourceDate.toDateString() === date.toDateString()

        // 判断是否为工作日（用于统计）
        let isWorkdayForStats = false
        if (holidayManager) {
          if (holidayInfo && holidayInfo.type !== 'normal') {
            isWorkdayForStats = holidayInfo.isWork
          } else if (holidayInfo && holidayInfo.type === 'normal') {
            isWorkdayForStats = true
          } else {
            isWorkdayForStats = dayOfWeek >= 1 && dayOfWeek <= 5
          }
        } else {
          isWorkdayForStats = dayOfWeek >= 1 && dayOfWeek <= 5
        }

        if (isWorkdayForStats) {
          currentMonthWorkdays++
          // 如果这个工作日被选中，增加已选择的工作日计数
          if (isSelectedTarget) {
            currentMonthSelectedWorkdays++
          }
        }

        // 检查是否在任职日期范围内
        const isInEmploymentRange = this._isDateInEmploymentRange(date)

        // 检查是否是今天
        const today = new Date()
        const isToday = date.toDateString() === today.toDateString()

        // 获取节假日名称
        let holidayName = ''
        if (holidayInfo && holidayInfo.name && holidayInfo.type !== 'normal') {
          holidayName = holidayInfo.name
        }

        calendarDays.push({
          day,
          date,
          hasData,
          isCurrentMonth: true,
          status,
          statusConfig,
          isSelectedTarget,
          isSelectedSource,
          dailyIncome: dayData.dailyIncome || 0,
          segmentCount: dayData.segments ? dayData.segments.length : 0,
          isWorkday: isWorkdayForStats,
          isInEmploymentRange,
          isToday,
          holidayInfo,
          holidayName
        })
      }

      // 检测数据冲突（仅在步骤2时检测）
      let hasDataConflict = false
      let conflictDatesCount = 0

      if (this.data.batchStep === 2 && this.data.selectedTargetDates && this.data.selectedTargetDates.length > 0) {
        if (this.batchCopyService) {
          const conflicts = this.batchCopyService.checkDataConflicts(this.data.selectedTargetDates, this.data.currentWorkId)
          hasDataConflict = conflicts.hasConflicts
          conflictDatesCount = conflicts.conflictCount
        } else {
          // 降级处理
          conflictDatesCount = this.data.selectedTargetDates.filter(targetDate => {
            const dayData = timeSegmentService.getDayData(targetDate)
            return (dayData.segments && dayData.segments.length > 0) ||
                   (dayData.fishingRecords && dayData.fishingRecords.length > 0) ||
                   (dayData.extraIncomeItems && dayData.extraIncomeItems.length > 0) ||
                   (dayData.deductionItems && dayData.deductionItems.length > 0)
          }).length
          hasDataConflict = conflictDatesCount > 0
        }
      }

      // 计算选择的日期范围
      const selectedDateRange = this._calculateSelectedDateRange()

      this.setData({
        batchCalendarDays: calendarDays,
        currentMonthWorkdays: currentMonthWorkdays,
        currentMonthSelectedWorkdays: currentMonthSelectedWorkdays,
        hasDataConflict: hasDataConflict,
        conflictDatesCount: conflictDatesCount,
        selectedDateRange: selectedDateRange
      })
    },

    /**
     * 检查日期是否在任职范围内
     */
    _isDateInEmploymentRange(date) {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (currentPage.isDateInEmploymentRange) {
          return currentPage.isDateInEmploymentRange(date)
        }
      }
      return true
    },

    /**
     * 计算选择的日期范围显示文本
     */
    _calculateSelectedDateRange() {
      const { selectedTargetDates } = this.data

      if (!selectedTargetDates || selectedTargetDates.length === 0) {
        return '未选择日期'
      }

      if (selectedTargetDates.length === 1) {
        return this._formatDateForRange(selectedTargetDates[0])
      }

      // 排序日期
      const sortedDates = [...selectedTargetDates].sort((a, b) => a.getTime() - b.getTime())
      const startDate = sortedDates[0]
      const endDate = sortedDates[sortedDates.length - 1]

      return `${this._formatDateForRange(startDate)} ~ ${this._formatDateForRange(endDate)}`
    },

    /**
     * 格式化日期用于范围显示
     */
    _formatDateForRange(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}/${month}/${day}`
    },

    /**
     * 检查是否是工作日
     */
    _isWorkday(date) {
      const holidayManager = this._getHolidayManager()
      if (holidayManager) {
        return holidayManager.isWorkday(date)
      }
      
      // 如果没有节假日管理器，简单判断周一到周五为工作日
      const dayOfWeek = date.getDay()
      return dayOfWeek >= 1 && dayOfWeek <= 5
    },

    /**
     * 检查日期是否被选为源日期
     */
    _isSelectedSource(date) {
      if (!this.data.selectedSourceDateObject) return false
      return this._isSameDate(date, this.data.selectedSourceDateObject)
    },

    /**
     * 检查日期是否被选为目标日期
     */
    _isSelectedTarget(date) {
      return this.data.selectedTargetDates.some(targetDate => 
        this._isSameDate(date, targetDate)
      )
    },

    /**
     * 检查两个日期是否相同
     */
    _isSameDate(date1, date2) {
      return date1.getFullYear() === date2.getFullYear() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getDate() === date2.getDate()
    },

    /**
     * 格式化日期
     */
    _formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    /**
     * 批量复制日历上月
     */
    onBatchPreviousMonth() {
      let { batchCalendarYear, batchCalendarMonth } = this.data

      batchCalendarMonth--
      if (batchCalendarMonth < 1) {
        batchCalendarMonth = 12
        batchCalendarYear--
      }

      this.setData({
        batchCalendarYear,
        batchCalendarMonth
      })

      this.generateBatchCalendar()
    },

    /**
     * 批量复制日历下月
     */
    onBatchNextMonth() {
      let { batchCalendarYear, batchCalendarMonth } = this.data

      batchCalendarMonth++
      if (batchCalendarMonth > 12) {
        batchCalendarMonth = 1
        batchCalendarYear++
      }

      this.setData({
        batchCalendarYear,
        batchCalendarMonth
      })

      this.generateBatchCalendar()
    },

    /**
     * 批量复制日历上一年
     */
    onBatchPreviousYear() {
      let { batchCalendarYear } = this.data

      batchCalendarYear--

      this.setData({
        batchCalendarYear
      })

      this.generateBatchCalendar()
    },

    /**
     * 批量复制日历下一年
     */
    onBatchNextYear() {
      let { batchCalendarYear } = this.data

      batchCalendarYear++

      this.setData({
        batchCalendarYear
      })

      this.generateBatchCalendar()
    },

    /**
     * 批量复制日历点击日期
     */
    onBatchDateTap(e) {
      const { index } = e.currentTarget.dataset
      const dayItem = this.data.batchCalendarDays[index]

      if (!dayItem || !dayItem.isCurrentMonth) return

      const date = dayItem.date

      if (this.data.batchStep === 1) {
        // 第一步：选择源日期
        if (!dayItem.hasData) {
          wx.showToast({
            title: '该日期没有日期安排或状态',
            icon: 'none'
          })
          return
        }

        // 获取该日期的日期安排预览
        const timeSegmentService = this._getTimeSegmentService()
        if (!timeSegmentService) {
          console.warn('timeSegmentService not available')
          return
        }

        const dayData = timeSegmentService.getDayData(date)
        const status = timeSegmentService.getDateStatus(date, this.data.currentWorkId)
        const statusConfig = status ? timeSegmentService.getDateStatusConfig(status) : null

        let displaySegments = []
        if (dayData.segments && dayData.segments.length > 0) {
          displaySegments = dayData.segments.map(segment => {
            const startTime = this._formatTime(segment.start)
            const endTime = this._formatTime(segment.end)
            const duration = this._formatDuration(segment.end - segment.start)
            const typeIcon = this._getTypeIcon(segment.type)
            const incomeText = segment.income ? segment.income.toFixed(2) : '0.00'
            const hourlyRateText = segment.hourlyRate ? segment.hourlyRate.toFixed(2) : null

            return {
              type: segment.type,
              typeText: this._getTypeText(segment.type),
              typeIcon,
              startTime,
              endTime,
              duration,
              income: segment.income || 0,
              incomeText,
              hourlyRateText
            }
          })
        }

        const schedulePreview = {
          date: this._formatDateText(date),
          dailyIncome: dayData.dailyIncome || 0,
          segments: displaySegments,
          statusConfig,
          hasSegments: displaySegments.length > 0,
          statusOnly: displaySegments.length === 0 && !!status
        }

        // 分析源数据类型
        const sourceDataTypes = this.batchCopyService ?
          this.batchCopyService.getAvailableCopyTypes(date, this.data.currentWorkId) :
          { hasSegments: displaySegments.length > 0, hasStatus: !!status }

        this.setData({
          selectedSourceDate: date,
          sourceSchedulePreview: schedulePreview,
          sourceDataTypes,
          showScrollIndicator: !this.data.hasShownScrollIndicator
        })

        // 标记已显示过滚动提示
        if (!this.data.hasShownScrollIndicator) {
          this.setData({
            hasShownScrollIndicator: true
          })
        }

        this.generateBatchCalendar()
      } else {
        // 第二步：选择目标日期
        // 检查是否是今天
        if (dayItem.isToday) {
          wx.showToast({
            title: '不能选择今天',
            icon: 'none'
          })
          return
        }

        const selectedTargetDates = [...this.data.selectedTargetDates]
        const existingIndex = selectedTargetDates.findIndex(targetDate =>
          targetDate.toDateString() === date.toDateString()
        )

        if (existingIndex >= 0) {
          // 已选中，取消选中
          selectedTargetDates.splice(existingIndex, 1)
        } else {
          // 未选中，添加选中
          selectedTargetDates.push(date)
        }

        this.setData({
          selectedTargetDates
        })

        this.generateBatchCalendar()
      }
    },

    /**
     * 获取类型图标
     */
    _getTypeIcon(type) {
      const typeMap = {
        'work': '💼',
        'rest': '😴',
        'overtime': '⏰'
      }
      return typeMap[type] || '📝'
    },

    /**
     * 添加当前月份的工作日到现有选择中
     */
    onAddCurrentMonthWorkdays() {
      console.log('添加本月工作日按钮被点击')
      const { batchCalendarYear, batchCalendarMonth, selectedTargetDates } = this.data
      const currentWorkdayDates = this._getCurrentMonthWorkdays()

      console.log('当前月份工作日:', currentWorkdayDates.length, '个')
      console.log('已选择的目标日期:', selectedTargetDates.length, '个')

      // 创建现有选择日期的字符串集合，用于快速查找
      const existingDatesSet = new Set(selectedTargetDates.map(date => date.toDateString()))

      // 过滤出尚未选择的工作日，并排除今天和有数据的日期
      const today = new Date()
      const todayString = today.toDateString()
      const timeSegmentService = this._getTimeSegmentService()

      const newWorkdayDates = currentWorkdayDates.filter(date => {
        // 排除周六日（双重保险）
        const dayOfWeek = date.getDay()
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          return false
        }

        // 排除已选择的日期
        if (existingDatesSet.has(date.toDateString())) {
          return false
        }

        // 排除今天
        if (date.toDateString() === todayString) {
          return false
        }

        // 排除不在任职范围内的日期
        if (!this._isDateInEmploymentRange(date)) {
          return false
        }

        // 排除有数据的日期（与日历显示逻辑保持一致）
        if (timeSegmentService) {
          const dayData = timeSegmentService.getDayData(date)
          const status = timeSegmentService.getDateStatus(date, this.data.currentWorkId)
          const hasData = (dayData.segments && dayData.segments.length > 0) || !!status

          if (hasData) {
            return false
          }
        }

        return true
      })

      // 合并现有选择和新的工作日
      const updatedSelectedDates = [...selectedTargetDates, ...newWorkdayDates]

      console.log('新增工作日:', newWorkdayDates.length, '个')
      console.log('更新后总选择:', updatedSelectedDates.length, '个')

      this.setData({
        selectedTargetDates: updatedSelectedDates
      })

      this.generateBatchCalendar()

      // 显示操作结果
      const addedCount = newWorkdayDates.length
      const totalSelected = updatedSelectedDates.length

      // 计算排除的日期数量
      const totalWorkdays = currentWorkdayDates.length
      const excludedCount = totalWorkdays - addedCount - selectedTargetDates.filter(date => {
        const dateString = date.toDateString()
        return currentWorkdayDates.some(workday => workday.toDateString() === dateString)
      }).length

      if (addedCount > 0) {
        let message = `已添加本月${addedCount}个工作日，总计选择${totalSelected}个日期`
        if (excludedCount > 0) {
          message += `（已排除${excludedCount}个有数据的日期）`
        }
        wx.showToast({
          title: message,
          icon: 'success',
          duration: 3000
        })
      } else {
        wx.showToast({
          title: '本月可选择的工作日已全部选择（已排除今天和有数据的日期）',
          icon: 'none',
          duration: 2500
        })
      }

      console.log(`添加了${batchCalendarYear}年${batchCalendarMonth}月的${addedCount}个工作日，总计选择${totalSelected}个日期`)
    },

    /**
     * 获取当前月份的所有工作日
     */
    _getCurrentMonthWorkdays() {
      const { batchCalendarYear, batchCalendarMonth } = this.data
      const workdayDates = []
      let holidayDataAvailable = false

      // 获取当月天数
      const daysInMonth = new Date(batchCalendarYear, batchCalendarMonth, 0).getDate()

      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(batchCalendarYear, batchCalendarMonth - 1, day)
        const dayOfWeek = date.getDay()

        // 跳过周六日，与日历显示保持一致
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          continue
        }

        // 优先使用节假日数据判断工作日
        let isWorkday = false
        const holidayManager = this._getHolidayManager()

        if (holidayManager) {
          const holidayInfo = holidayManager.getDateInfo(date)

          if (holidayInfo && holidayInfo.type !== 'normal') {
            // 如果有具体的节假日信息（非普通日期）
            holidayDataAvailable = true
            isWorkday = holidayInfo.isWork
          } else if (holidayInfo && holidayInfo.type === 'normal') {
            // 普通日期，不是节假日也不是周末
            isWorkday = true
          } else {
            // 如果没有节假日信息，按照周一到周五判断
            isWorkday = dayOfWeek >= 1 && dayOfWeek <= 5 // 周一到周五
          }
        } else {
          // 如果节假日管理器不可用，按照周一到周五判断
          isWorkday = dayOfWeek >= 1 && dayOfWeek <= 5 // 周一到周五
        }

        if (isWorkday && this._isDateInEmploymentRange(date)) {
          workdayDates.push(date)
        }
      }

      return workdayDates
    },

    /**
     * 清空选择的目标日期
     */
    onClearSelection() {
      // 如果没有选择的日期，直接返回
      if (this.data.selectedTargetDates.length === 0) {
        return
      }

      // 二次确认
      const that = this;
      wx.showModal({
        title: '提示',
        content: '确定要清空选择吗？',
        success (res) {
          console.log(res)
          if (res.confirm) {
            const clearedCount = that.data.selectedTargetDates.length
            that.setData({
              selectedTargetDates: []
            })
            that.generateBatchCalendar()
            console.log(`清空了${clearedCount}个选择的日期`)
          }
        }
      })

    },

    /**
     * 格式化时间（分钟转时间显示）
     */
    _formatTime(minutes) {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`
    },

    /**
     * 获取类型文本
     */
    _getTypeText(type) {
      const typeMap = {
        'work': '工作',
        'rest': '休息',
        'overtime': '加班'
      }
      return typeMap[type] || type
    },

    /**
     * 格式化持续时间
     */
    _formatDuration(minutes) {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60

      if (hours > 0 && mins > 0) {
        return `${hours}小时${mins}分钟`
      } else if (hours > 0) {
        return `${hours}小时`
      } else {
        return `${mins}分钟`
      }
    },

    /**
     * 选择模板日期（导入模式）
     */
    onSelectTemplate(e) {
      const { index } = e.currentTarget.dataset
      const templateItem = this.data.templateDates[index]

      if (!templateItem) return

      // 直接导入到当前选中的日期
      this._copyScheduleFromDate(templateItem.date, [this.data.selectedDate])

      this.onClose()
    },

    /**
     * 确认选择源日期
     */
    onConfirmSourceDate() {
      if (!this.data.selectedSourceDate) {
        wx.showToast({
          title: '请选择源日期',
          icon: 'none'
        })
        return
      }

      this.setData({
        batchStep: 2
      })

      this.generateBatchCalendar()
    },

    /**
     * 返回选择源日期
     */
    onBackToSourceSelection() {
      this.setData({
        batchStep: 1,
        selectedTargetDates: []
      })

      this.generateBatchCalendar()
    },



    /**
     * 批量模态框滚动事件
     */
    onBatchModalScroll() {
      // 滚动时隐藏提示箭头
      if (this.data.showScrollIndicator) {
        this.setData({
          showScrollIndicator: false
        })
      }
    },

    /**
     * 确认批量操作
     */
    onConfirmBatchOperation() {
      if (this.data.operation === 'import') {
        // 导入模式已经在onSelectTemplate中处理
        return
      }

      // 批量复制模式
      if (!this.data.selectedSourceDate) {
        wx.showToast({
          title: '请选择源日期',
          icon: 'none'
        })
        return
      }

      if (this.data.selectedTargetDates.length === 0) {
        wx.showToast({
          title: '请选择目标日期',
          icon: 'none'
        })
        return
      }

      // 防止重复点击
      if (this.data.batchCopyInProgress || this.data.batchCopyCompleted) {
        return
      }

      // 设置复制进行中状态
      this.setData({
        batchCopyInProgress: true
      })

      try {
        this._copyScheduleFromDate(this.data.selectedSourceDate, this.data.selectedTargetDates)

        // 复制成功，立即关闭模态框
        this.setData({
          batchCopyInProgress: false,
          batchCopyCompleted: true
        })

        // 短暂延迟后关闭，让用户看到完成状态
        setTimeout(() => {
          this.onClose()
        }, 500)
      } catch (error) {
        // 复制失败，重置状态
        this.setData({
          batchCopyInProgress: false,
          batchCopyCompleted: false
        })
        console.error('复制操作失败:', error)
      }
    },

    /**
     * 复制日期安排从指定日期到目标日期
     */
    _copyScheduleFromDate(sourceDate, targetDates) {
      if (!this.batchCopyService) {
        console.warn('BatchCopyService not available')
        wx.showToast({
          title: '服务未初始化',
          icon: 'none'
        })
        return
      }

      try {
        // 使用新的批量复制服务
        const result = this.batchCopyService.selectiveCopy(
          sourceDate,
          targetDates,
          {
            copyStatus: this.data.copyStatus,
            copySegments: this.data.copySegments
          },
          this.data.currentWorkId
        )

        console.log('批量复制结果:', result)

        if (result.success) {
          // 通知页面数据已更新
          this.triggerEvent('dataUpdated', {
            operation: 'copy',
            sourceDate: sourceDate.toISOString ? sourceDate.toISOString() : sourceDate,
            targetDates: targetDates.map(date => date.toISOString()),
            count: result.successCount,
            copyType: result.copyType
          })

          let message = `成功复制${result.copyType}到${result.successCount}个日期`
          if (result.failCount > 0) {
            message += `，${result.failCount}个失败`
          }

          wx.showToast({
            title: message,
            icon: result.failCount > 0 ? 'none' : 'success',
            duration: 3000
          })
        } else {
          throw new Error('复制操作失败')
        }
      } catch (error) {
        console.error('批量操作组件：复制失败', error)
        wx.showToast({
          title: error.message || '复制失败',
          icon: 'none'
        })
      }
    },

    /**
     * 取消
     */
    onCancel() {
      this.triggerEvent('cancel')
      this.onClose()
    },

    /**
     * 关闭模态框
     */
    onClose() {
      // 开始出场动画
      this.setData({ modalVisible: false })

      // 等待动画完成后触发关闭事件
      setTimeout(() => {
        this.triggerEvent('close')
      }, 300) // 与CSS动画时长一致
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止点击模态框内容时关闭模态框
    },

    /**
     * 加载组件数据
     */
    _loadComponentData() {
      try {
        // 获取当前工作ID
        const pages = getCurrentPages()
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1]
          if (currentPage.data && currentPage.data.currentWorkId) {
            this.setData({
              currentWorkId: currentPage.data.currentWorkId
            })
          }
        }

        // 获取当前日期
        const now = new Date()
        const currentDate = this._formatDateText(now)

        this.setData({
          currentDate
        })

        console.log('批量操作组件：数据加载完成', {
          currentWorkId: this.data.currentWorkId,
          currentDate
        })
      } catch (error) {
        console.warn('批量操作组件：数据加载失败', error)
      }
    },

    /**
     * 格式化日期文本
     */
    _formatDateText(date) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${year}年${month}月${day}日`
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('批量操作组件已加载')
    }
  },

  /**
   * 组件观察器
   */
  observers: {
    'visible': function(visible) {
      this._onPropertiesChange()
    }
  }
})
