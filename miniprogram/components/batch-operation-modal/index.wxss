/* 批量操作模态框组件样式 */

/* 模态框基础样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;

  /* 使用统一的transition动画 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #fff;
  border-radius: 36rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.show .modal-content {
  transform: scale(1) translateY(0);
}

.batch-modal-content {
  width: 90%;
  max-width: 800rpx;
  max-height: 90vh; /* 增加高度 */
  overflow-y: auto;
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 1rpx solid #E5E7EB;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:active {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transform: scale(0.95);
}

/* 模态框主体 */
.modal-body {
  padding: 20rpx 40rpx;
  max-height: 50vh;
  overflow-y: auto;
}

/* 批量操作模态框样式 */
.modal-subtitle {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 24rpx;
}

.template-list {
  max-height: 400rpx;
  overflow-y: scroll;
  margin-bottom: 24rpx;
}

.template-item {
  background: #F9FAFB;
  border: 1rpx solid #E5E7EB;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.template-item:active {
  background: #F3F4F6;
  transform: scale(0.98);
}

.template-selected {
  background: #EFF6FF;
  border-color: #3B82F6;
}

.template-date {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.date-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1F2937;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.status-icon {
  font-size: 20rpx;
}

.status-text {
  font-size: 20rpx;
  font-weight: 500;
}

.template-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.segment-count {
  font-size: 24rpx;
  color: #6B7280;
}

.income-info {
  font-size: 24rpx;
  color: #059669;
  font-weight: 500;
}

/* 空状态 */
.empty-templates {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #9CA3AF;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  color: #6B7280;
}

.empty-tip {
  font-size: 28rpx;
  color: #9CA3AF;
}

/* 数据冲突警告 */
.data-conflict-warning {
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(239, 68, 68, 0.3);
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.warning-icon {
  font-size: 24rpx;
}

.warning-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #DC2626;
}

.warning-text {
  font-size: 24rpx;
  color: #7F1D1D;
  line-height: 1.5;
}

/* 快速添加工作日按钮 */
.quick-add-workdays {
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

.quick-add-btn {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  background: #00b8a9;
  border-radius: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.quick-add-btn:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(102, 126, 234, 0.4);
}

.quick-add-btn.clear-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.3);
}

.quick-add-btn.clear-btn:active {
  box-shadow: 0 1rpx 4rpx rgba(239, 68, 68, 0.4);
}

.quick-add-btn.disabled {
  background: #E5E7EB !important;
  box-shadow: none !important;
  cursor: not-allowed;
}

.quick-add-btn.disabled .btn-icon,
.quick-add-btn.disabled .btn-text {
  color: #9CA3AF !important;
}

.quick-add-btn.disabled:active {
  transform: none !important;
  box-shadow: none !important;
}

.quick-add-btn .btn-icon {
  font-size: 24rpx;
  color: white;
}

.quick-add-btn .btn-text {
  font-size: 26rpx;
  color: white;
  font-weight: 500;
}

/* 滚动提示箭头 */
.scroll-indicator {
  position: absolute;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  pointer-events: none;
  opacity: 0;
}

.scroll-indicator.fade-in-out {
  animation: fadeInBounceFloat 3s ease-in-out forwards;
}

.scroll-arrow {
  display: flex;
  gap: 12rpx;
  align-items: center;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.arrow-icon {
  font-size: 24rpx;
  font-weight: bold;
  animation: floatUpDown 2s ease-in-out infinite;
}

.arrow-text {
  font-size: 20rpx;
  white-space: nowrap;
  font-weight: 500;
}

@keyframes fadeInBounceFloat {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(20rpx);
  }
  10% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  90% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
}

@keyframes floatUpDown {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6rpx);
  }
}

/* 批量日历样式 */
.batch-calendar {
  margin: 20rpx 0;
}

.batch-calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

.batch-calendar-nav {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f8f9fa;
}

.batch-calendar-nav:active {
  background-color: #e9ecef;
}

.nav-arrow {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}

.batch-calendar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
}

.batch-calendar-weekdays {
  display: flex;
  margin-bottom: 10rpx;
}

.batch-calendar-weekdays .weekday {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #6c757d;
  padding: 10rpx 0;
}

.batch-calendar-days {
  display: flex;
  flex-wrap: wrap;
  gap: 0; /* 移除间距 */
}

.batch-calendar-day {
  width: 20%; /* 5列布局：100% / 5 = 20% */
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  padding: 4rpx;
  box-sizing: border-box;
  /* 移除边框和圆角 */
}

.batch-calendar-day.current-month {
  cursor: pointer;
}

/* 批量日历状态样式 - 与主日历保持一致 */
/* 工作/出勤状态类 - 蓝色系 */
.batch-calendar-day.day-status-work,
.batch-calendar-day.day-status-rest,
.batch-calendar-day.day-status-rotation_rest,
.batch-calendar-day.day-status-compensatory_rest,
.batch-calendar-day.day-status-work_suspension,
.batch-calendar-day.day-status-standby,
.batch-calendar-day.day-status-duty {
  background: var(--status-bg-color, #EFF6FF);
}

.batch-calendar-day.day-status-work .day-number,
.batch-calendar-day.day-status-rest .day-number,
.batch-calendar-day.day-status-rotation_rest .day-number,
.batch-calendar-day.day-status-compensatory_rest .day-number,
.batch-calendar-day.day-status-work_suspension .day-number,
.batch-calendar-day.day-status-standby .day-number,
.batch-calendar-day.day-status-duty .day-number {
  color: var(--status-color, #3B82F6);
}

/* 节假日类 - 绿色系 */
.batch-calendar-day.day-status-holiday,
.batch-calendar-day.day-status-annual_leave,
.batch-calendar-day.day-status-festival_leave {
  background: var(--status-bg-color, #ECFDF5);
}

.batch-calendar-day.day-status-holiday .day-number,
.batch-calendar-day.day-status-annual_leave .day-number,
.batch-calendar-day.day-status-festival_leave .day-number {
  color: var(--status-color, #10B981);
}

/* 请假/缺勤类 - 橙色系 */
.batch-calendar-day.day-status-leave,
.batch-calendar-day.day-status-sick,
.batch-calendar-day.day-status-marriage_leave,
.batch-calendar-day.day-status-maternity_leave,
.batch-calendar-day.day-status-paternity_leave,
.batch-calendar-day.day-status-bereavement_leave,
.batch-calendar-day.day-status-work_injury_leave,
.batch-calendar-day.day-status-family_visit_leave,
.batch-calendar-day.day-status-absent {
  background: var(--status-bg-color, #FFFBEB);
}

.batch-calendar-day.day-status-leave .day-number,
.batch-calendar-day.day-status-sick .day-number,
.batch-calendar-day.day-status-marriage_leave .day-number,
.batch-calendar-day.day-status-maternity_leave .day-number,
.batch-calendar-day.day-status-paternity_leave .day-number,
.batch-calendar-day.day-status-bereavement_leave .day-number,
.batch-calendar-day.day-status-work_injury_leave .day-number,
.batch-calendar-day.day-status-family_visit_leave .day-number,
.batch-calendar-day.day-status-absent .day-number {
  color: var(--status-color, #F59E0B);
}

/* 今天禁用状态 */
.batch-calendar-day.is-today {
  background-color: #F5F5F5 !important;
  color: #9E9E9E !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.batch-calendar-day.is-today .day-number {
  color: #9E9E9E !important;
}

.batch-calendar-day.is-today .holiday-name {
  color: #9E9E9E !important;
}

/* 选中的源日期 - 保留边框 */
.batch-calendar-day.selected-source {
  background-color: #C8E6C9 !important;
  border: 2rpx solid #4CAF50 !important;
  box-sizing: border-box !important;
}

.batch-calendar-day.selected-source .day-number {
  font-weight: 700 !important;
  color: #2E7D32 !important;
}

.batch-calendar-day.selected-source .holiday-name {
  color: #2E7D32 !important;
}

@keyframes breathing {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 选中的目标日期 - 绿色系 */
.batch-calendar-day.selected-target {
  background-color: #C8E6C9 !important;
  animation: breathing 1.5s infinite ease-in-out;
}

.batch-calendar-day.selected-target .day-number {
  font-weight: 700 !important;
  color: #2E7D32 !important;
}

.batch-calendar-day.selected-target .holiday-name {
  color: #2E7D32 !important;
}



.batch-calendar-day .day-number {
  font-size: 26rpx;
  color: #212529;
  font-weight: 500;
  z-index: 1;
}

.batch-calendar-day .holiday-name {
  font-size: 18rpx;
  color: #6B7280;
  text-align: center;
  line-height: 1;
  margin-top: 2rpx;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.batch-calendar-day .today-indicator {
  font-size: 16rpx;
  color: #007aff;
  text-align: center;
  line-height: 1;
  margin-top: 2rpx;
  font-weight: 600;
}

/* 批量日历中不在任职范围内的日期样式 */
.batch-calendar-day.day-outside-employment .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

.batch-calendar-day.day-outside-employment .holiday-name {
  color: #9ca3af !important;
  opacity: 0.6;
}

/* 确保批量日历中不在任职范围内的日期在各种状态下都保持灰色 */
.batch-calendar-day.day-outside-employment.has-data .day-number,
.batch-calendar-day.day-outside-employment.selected-source .day-number,
.batch-calendar-day.day-outside-employment.selected-target .day-number,
.batch-calendar-day.day-outside-employment.selected .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

.batch-calendar-day.day-outside-employment.has-data .holiday-name,
.batch-calendar-day.day-outside-employment.selected-source .holiday-name,
.batch-calendar-day.day-outside-employment.selected-target .holiday-name,
.batch-calendar-day.day-outside-employment.selected .holiday-name {
  color: #9ca3af !important;
  opacity: 0.6;
}

.batch-calendar-day .day-indicator {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  background-color: rgba(33, 150, 243, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.batch-calendar-day .indicator-text {
  font-size: 16rpx;
  color: white;
  font-weight: 600;
}

/* 批量日历状态图标 */
.batch-calendar-day .status-indicator {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  width: 20rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.batch-calendar-day .status-indicator .status-icon {
  font-size: 16rpx;
  line-height: 1;
}

/* 工作安排预览 */
.schedule-preview {
  margin-top: 36rpx;
  padding: 20rpx;
  border: 1rpx solid #dcdcdc;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 24rpx;
}

.preview-header {
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.preview-title-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.preview-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #212529;
}

.preview-status {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 8rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8rpx;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}

.preview-status .status-icon {
  font-size: 16rpx;
}

.preview-status .status-text {
  font-size: 20rpx;
  color: #3B82F6;
  font-weight: 500;
}

.preview-income-section {
  text-align: right;
  margin-top: 20rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.preview-income {
  font-size: 26rpx;
  color: #059669;
  font-weight: 600;
}

.preview-segments {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.preview-segment {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.segment-left {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.segment-type-badge {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.segment-type-badge.type-work {
  background: rgba(59, 130, 246, 0.1);
  color: #3B82F6;
}

.segment-type-badge.type-rest {
  background: rgba(16, 185, 129, 0.1);
  color: #10B981;
}

.segment-type-badge.type-overtime {
  background: rgba(245, 158, 11, 0.1);
  color: #F59E0B;
}

.type-text {
  font-size: 20rpx;
}

.segment-time-info {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}

.segment-time {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
}

.segment-duration {
  font-size: 20rpx;
  color: #6B7280;
}

.segment-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2rpx;
}

.segment-income-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2rpx;
}

.income-amount {
  font-size: 24rpx;
  color: #059669;
  font-weight: 600;
}

.hourly-rate {
  font-size: 20rpx;
  color: #6B7280;
}

/* 只有状态的预览 */
.status-only-preview {
  padding: 20rpx;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12rpx;
  border: 1rpx dashed rgba(59, 130, 246, 0.3);
  text-align: center;
}

.status-only-info {
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-only-text {
  font-size: 24rpx;
  color: #6B7280;
  font-style: italic;
}

/* 批量选择状态（固定在底部） */
.batch-selection-status {
  position: sticky;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  padding: 16rpx 20rpx;
  margin: 0 -20rpx -20rpx -20rpx;
}

.selection-summary {
  display: flex;
  justify-content: center;
  align-items: center;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  white-space: nowrap; /* 防止换行 */
}

.summary-label {
  font-size: 24rpx;
  color: #6B7280;
  white-space: nowrap;
}

.summary-value {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
}

.summary-count {
  font-size: 24rpx;
  color: #6B7280;
  font-weight: 500;
  white-space: nowrap;
}

.holiday-status-mini {
  text-align: center;
  padding: 4rpx 8rpx;
  background: rgba(34, 197, 94, 0.1);
  border-radius: 6rpx;
}

.holiday-status-mini .status-text {
  font-size: 20rpx;
  color: #059669;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 40rpx;
  border-top: 1rpx solid #E5E7EB;
  background: #F9FAFB;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
}

.btn-secondary {
  background: #F3F4F6;
  color: #6B7280;
}

.btn-secondary:active {
  background: #E5E7EB;
  color: #374151;
  transform: scale(0.98);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.btn-primary.disabled {
  background: #E5E7EB;
  color: #9CA3AF;
  box-shadow: none;
  cursor: not-allowed;
}

.btn-primary.disabled:active {
  transform: none;
  box-shadow: none;
}

/* 复制信息面板 */
.copy-info-panel {
  margin: 30rpx 0;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
}

.copy-note {
  margin-top: 20rpx;
  padding: 16rpx 20rpx;
  background-color: #e3f2fd;
  border-radius: 12rpx;
  border-left: 6rpx solid #2196f3;
}

.note-text {
  font-size: 24rpx;
  color: #1976d2;
  line-height: 1.4;
}
